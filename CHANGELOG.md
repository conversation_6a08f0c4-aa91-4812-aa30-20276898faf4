# Changelog

Traces of your evolution, detailed below.

## [3.0.2] - - 2025-07-31

### 🚀 Features

- 灵感源列表、现货商品池、AIGC选款、AIGC选款结果增加款式信息
- 仿款提交任务增加款式信息
- 选款确认fix
- 选款详情fix
- 调整字段类型
- 灵感源列表-图案提取新增字段
- 灵感源列表-SDK查询
- 调整版本
- 调整字段赋值
- 增加企划类型
- 国家站点还原
- 版本升级
- *(picking)* 新增模特参考图DTO及相关功能 新增TryOnModelReferenceImageDTO类，并在PickStyleConvert、PickingAiDesignStyle、PickingConfirmReq和DesignDemandCreateReq中增加模特参考图相关字段处理逻辑
- *(inspiration)* 在多个请求类中添加商品图集合字段
- *(灵感仿款)* 新增1688图搜商品仿款支持 - 在InspirationImitationConfirmReq等请求类中添加skc1688List字段 - 新增Product1688SelectionStyleReq用于1688图搜选款 - 在InspirationConvert和ServiceImpl中处理1688商品数据 - 扩展InspirationProductClient接口支持1688图搜选款 - 优化InspirationImitationComponent同时处理常规和1688商品选款逻辑
- *(ci)* 新增GitLab CI配置文件

### 🐛 Bug Fixes

- *(InspirationServiceImpl)* 增加skc1688List空校验及字段校验 校验请求参数中skc1688List为空的情况，并添加skcColor字段的非空检查
- *(InspirationServiceImpl)* 增加对skc1688List的校验逻辑

## [3.0.1] - 2025-06-24

### 🚀 Features

- 灵感openapi, 调整user相关类
- 迁移AI设计选款相关接口
- Sql
- 灵感数据编号
- 依赖灵感设计SDK
- Sql脚本,依赖字典sdk
- 选款分页
- 导入
- AIGC选款数据库字段调整
- AIGC选款-导入
- AIGC选款-结果分页和导入
- AIGC选款-结果分页和详情
- AIGC选款-增加货盘字段
- AIGC选款-调整提交参数
- AIGC选款-提交
- AIGC选款-注释SDK(存在问题)
- 对接LOGO SDK
- 对接识别和AI设计SDK
- 灵感对接识别逻辑
- 灵感数据-处理标签返回
- 灵感数据-对接AI设计
- 灵感数据-回调数据新增选款
- 灵感数据-图片导入
- 灵感数据
- 选款相关回调
- Openapi
- 兼容design sdk的旧框架
- 调整字段
- 调整选中逻辑
- 调整design sdk
- 调整灵感字段
- 调整选款字段
- Butted sdk调整
- 对接auth
- 选款导入
- 调整选款分页结构
- 调整选款选中结构
- 查询统计企划供给数量
- 落坑统计接口
- 导出
- 数码印花定时接口拉取状态
- 调试sdk
- 解决log输出问题
- Commit
- 定时任务API
- 字典sdk
- 灵感相关字典
- 灵感-移除识别catch
- 增加AIGC提交校验
- AIDC趋势中心 接口对接
- AIDC趋势中心对接-dto
- *(picking)* 新增修图筛选功能并优化选款结果页面 - 在 PickingAiDesignResultMapper 中添加 fixImageType筛选条件 - 在 PickingStyleResultPageReq 中添加 fixImageType 字段 - 在 PickingStyleResultPageVo 中添加 inspirationCode 字段 - 重构 PickingStyleServiceImpl 中的 pageResult 方法，优化数据处理逻辑 - 更新版本号至 20241216
- *(picking)* 添加修图数据导出功能
- AIDC趋势中心-拉取灵感数据
- 灵感列表增加-灵感编号
- 提供feign sdk-商品上架通知接口
- 商品上架通知-保存关系记录
- 淘汰原因改为多选
- 提供选款SDK接口给AI设计
- 拉取AE灵感数据
- Test
- Sdk调整
- 创建用户信息
- AE价格处理
- AIDC趋势中心增加商品链接
- 调整灵感分页参数和选款导入校验
- AE初始时间
- 灵感增加筛选条件-企划来源和数据来源
- 增加临时修复AIDC新字段-商品链接
- 灵感导出增加商品链接字段
- 灵感模特新参数
- 灵感模特新参数-调整前端结构
- 灵感需求创建接口增加aigc_remark字段
- 灵感需求创建接口增加AI任务字段
- SDK RELEASE
- 处理历史选款数据
- 调整选款结果分页接口,兼容历史数据
- 调整选款结果详情接口,兼容历史数据
- 处理历史选款数据,更新状态
- 优化
- 兼容灵感图展示
- 1.仿款增加期望成本价 2.选款增加场景
- 对接sdp-design
- 对接sdp-design 提交人
- 重试AI设计任务
- 灵感任务client配置域名方式提供
- 选款的时候传递操作人
- 推送下游企划来源
- 单个任务抛异常
- V3.10.1 接口文档
- 接口文档
- 升级3.0
- 升级3.0&同步自定义client
- Oss sdk切换
- 提交灵感传入模型
- 兼容依赖sdk （临时方案）
- 引入springcore
- 引入springcore 兼容类
- 服务client自己写
- 不强制版本了
- 自定义client 拦截器
- Ai设计任务传递模型
- 重试ai设计 带上模型
- 合并master
- Ai设计修改参数后重试
- 图片导入灵感品牌来源
- 图片履约情况查询
- 选款传入商品主题
- Feign 拦截器
- 调用sdp-design换成走网关的形式
- Ai设计生图传递履约增强
- Ai设计履约查询
- Ai设计履约增强传递需要查询下
- 选款结果列表增加款号查询条件
- 灵感图片导入灵感图的来源和品牌
- 灵感excel导入 保存灵感来源和灵感图品牌的code
- 灵感excel导入和图片导入校验灵感来源和灵感图品牌
- 灵感excel导入和图片导入校验灵感来源和灵感图品牌,入参可能是空串，要判断下
- 详情返回商品主题和灵感源品牌
- 选款的时候传递附件给下游
- 单独提交灵感任务和aigc的时候，让用户传入品类
- 3.0.0-RELEASE
- 15号上线的版本先注释
- Ai生图传递参考值
- 推送4K图到sdp-design
- Model_code
- 过滤需要更新的数据
- Mode
- 确认选款结果
- 图片重试次数加1
- 推送sdp
- 推送sdp成功记录 推送状态
- 优化调用图案标签识别任务 1.去掉直接new 线程 2.withSystem后置，减少mock token失效的情况
- 提交选款推送商品主题
- 返回convert的值
- 单独提交灵感 供给方式=AIGC时，增加【品类】字段
- 提交灵感的时候，多个提交的时候，品类和同步品类不要传
- 单个提交的时候才需要重置品类
- 灵感仿款提交接口
- 灵感仿款提交接口生成
- 相似款 同款client引入
- 相似款 详情查询
- 同款标签
- 灵感增加参数
- 内部拆版 参数传递到sdp-design
- 仿款相关接口文档
- 详情返回相似款品类
- 定时任务跑智脑的图
- 记录更新关系
- 更新灵感 更新inspirationAidcSourceHistory
- 灵感列表和详情返回更新时间
- 处理灵感图
- 处理更新逻辑
- 处理mq发送
- 不用mq了
- 展示拉10段
- 灵感列表查询时间
- 暂时改下 segment
- 拉ae数据恢复到256段
- 已提交的数据要更新时间
- 详情返回更新时间
- 仿款提交 参数校验
- 仿款提交 接口文档
- 仿款提交
- 灵感详情
- Mq处理同款标签
- 同款标签处理
- 调整仿款提交
- 定时拉的灵感和导入的灵感 需要插入款式相似度
- 详情返回同款货源
- 计算价格标签
- 价格标签计算
- 风格名称
- 确认提交 参数校验
- 触发同款标签生成
- 保存同款ID
- 传递款式标签
- 传递提交记录提交日志
- 选款提交 保存提交日志
- 仿款再次提交接口
- 详情返回同源标签
- 详情返回同源标签，解决没用户调用问题
- 先返回结果再去异步更新标签
- 记录查询相似度日志
- 仿款提交、确认再次提交 校验spu 和 skc
- 灵感列表返回同款标签 和支持标签查询
- 仿款再次提交
- 仿款再次提交 接口改造
- 同款价格计算
- 详情返回同款价格
- 重新提交仿款接口
- 价格标签计算 过滤
- 详情接口优化
- 仿款再次提交 现货选款才校验spu
- 提交仿照抛出异常
- 现货选款提交 传递供给方式
- 同款货源查询 传入灵感ID
- 添加日志
- 没有售价，也不用做价格对比
- 匹配不到灵感的也要保存相似款数据
- 相似款数据保存expandBusCode
- 拉取灵感
- 定时插入相似款
- 状态更新问题
- 刷数
- 刷数过滤灵感图为空的
- 详情接口调别人接口不要报错
- 分批刷数据
- 选款提交新增视觉需求信息

### 🐛 Bug Fixes

- 修复日志冲突依赖
- 灵感,选款 修复分页筛选条件
- 修复表名调整
- 补充系统用户
- 联调提交任务
- 灵感图识别增加识别中状态
- 灵感设计-国家code
- Job
- 选款图片赋值错误
- 适配前端字段
- 波段字典调整
- 提交任务类型
- 数码印花版本更新
- 识别回调状态处理
- 选款标选
- 灵感提交次数递增
- Commit
- 导入
- 供给方式判断
- Enum
- 灵感提交灵感设计-波次
- 选款详情name字段
- 字典调整
- 灵感标签返回name
- 对接AI跑图回调
- 选款提交下游id
- 识别品类name
- 款式类型
- 选款-提交-建议国家可选
- 时间分页参数
- 选款数据来源
- 选款图片第一张为主图
- 回调创建选款的创建人
- 回调创建选款的创建人-测试
- 落坑接口url调整, AIGC SDK 更新, AIGC场景模特参数调整
- 推送AIGC补充itemId参数
- 日志状态
- 导出标签
- 移除选款导入的备注字段
- Json
- 选款导入
- 选款记录
- 修复选款导入,灵感详情增加波次字段
- 灵感提交任务都更新波次
- 修复AI设计回调
- 选款详情
- 选款列表增加是否选过状态
- 波次展示错误 #QX-00005320
- 灵感异步提交任务
- 建议印花
- 波次
- 选款结果AI任务信息
- 注解
- 修复导入选款
- 修复导入灵感
- 字典入参空串
- 灵感筛选时间
- 灵感批量提交任务时的事务隔离
- 捕捉异常
- 筛选灵感时间改为表的创建时间, 不是用AIDC的时间
- QX-00008850 【后端】灵感源导入--新增到向量库时，需要将外部品类传到向量库
- *(StyleLabel)* 移除suggestedShopCode字段并新增场景和买手字段
- *(InspirationServiceImpl)* 调整参数验证顺序并修复空值检查 优先验证imitationType枚举有效性，重构SELECTION类型的参数检查逻辑
- *(InspirationServiceImpl)* 修复仿款类型校验及枚举使用问题 refactor(InspirationConvert): 添加imitationType字段转换

### 💼 Other

- 选款-调整字段
- 选款-回显
- 灵感回调
- 注释
- 数码印花sdk
- 数码印花 调度
- 优化
- Test
- 数码印花增加灵感字段
- 模特增加name和url字段
- 商品上架通知内部接口
- AI设计编号展示
- AIGC增加2个新参数
- 优先使用修复图
- 灵感导入调整响应结构
- 选款增加下游日志
- 面料匹配
- 选款列表筛选款式的选中状态
- 过滤面料
- 选款结果创建人筛选
- 落坑数统计调整规则
- 选款筛选条件
- 选款提交灵感设计增加货盘字段
- 选款提交灵感设计店铺字段
- 若图片识别品类为空,赋值为"其他"品类
- 选款提交增加校验"当前没有可用结果图"
- 美化导出
- 没有商品id的面料部展示
- 修复和调整
- 传给下游, 优先主图在第一位
- 导入灵感和选款图片转存OSS
- 拉取AIDC灵感图片转存OSS
- 提供sdk接口根据灵感id/选款id查询(pop服务调用)
- 优化灵感导入异步
- Sdk增加选款时间字段
- 批量导入优化
- 外链转oss超时
- 放开AIDC同步数量限制
- 供给方式枚举code更新
- 灵感sdk增加字段
- AI任务调整品类code参数
- 下游给结果id
- 图片转oss增加域名黑名单
- 选款结果详情增加开款字段
- 字段调整
- 统计落坑数-增加shopId
- Ai sdk 更新release版本
- 顺序2000条,大约1小时
- 重新对接字典接口
- 调整字典key
- AE灵感开始时间
- 字段类型
- 模特不为空时, 则关闭脸部修复
- AIGC跑图-履约增强默认开启
- SDK-RELEASE
- 1. 0.0.3-RELEASE 版本发布 2. sdp-design 1.0.4-RELEASE
- 去除多余依赖
- 去除多余配置
- 排除poi-ooxml-schemas
- Implementation("tech.tiangong.inspiration:inspiration-sdk:3.0.5-RELEASE")
- 3.0.1-RELEASE

### 🚜 Refactor

- *(sdp)* 重构选款结果导出功能
- *(sdp-curation-service)* 移除 pickingStyle 结果数据过滤逻辑 移除了 PickingStyleServiceImpl 中对 resultImageInfo 进行修图类型过滤的代码。这可能是为了简化代码结构或更改数据处理逻辑。
- *(sdp-curation-service)* 优化字典客户端的日志记录和异常处理
- *(sdp-curation-service)* 优化获取灵感数据的逻辑- 在获取灵感数据时，增加了对 inspirationIds 可能为空的处理
- *(sdp-curation-service)* 优化包装回调服务中的选款逻辑 - 移除了注释掉的代码行inspirationId不需要 - 修复了 createSpuTime 为 null 时的处理逻辑，现在使用当前时间作为默认值
- *(sdp-curation-service)* 重构 PackingCallbackServiceImpl 类 - 优化 eliminate 和 openStyle 方法的逻辑结构 - 提高代码可读性和维护性 -增加日志记录，便于问题排查 - 采用 try-catch 增强异常处理
- *(sdp-curation-service)* 优化导出 Excel 任务的图片 URL 处理 - 创建动态图片 URL 表头，适应不同数量的图片 -调整表头顺序，将图片 URL 放在最后 -优化数据填充逻辑，提高代码可读性和维护性
- 修改图片列表字段类型
- 修改字段长度
- Aigc选款字段修改

### 📚 Documentation

- 定时任务日志
- Sql脚本
- 更新日志
- 定时日志
- 刷入灵感更新时间
- 接口文档
- 整理sql
- Sql整理

### ⚡ Performance

- 减少网络请求
- 优先修复图
- 导入
- 灵感导入-增加异步和多线程
- 选款导入-增加异步和多线程
- AIDC
- 优化代码：不在循环里面调用数据库

### 🎨 Styling

- 重写提交灵感接口
- 线程池配置返回ThreadPoolTaskExecutor，解决工具报警🚔

### 🧪 Testing

- 测试类
- 灵感列表接口单元测试
- 单元测试
- 仿款提交测试
- 测试查询接口

📝 Generated by <strong>aikero-ci-robot</strong> with ❤️
