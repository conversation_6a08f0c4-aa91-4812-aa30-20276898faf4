import team.aikero.gradle.plugin.version.catalog.versionCatalogConf

plugins {
    id("team.aikero.gradle.plugin.version-catalog")
    id("org.gradle.toolchains.foojay-resolver-convention")
}
rootProject.name = "sdp-curation"
val frameworkVersion: String by settings

include(
    "sdp-curation-common",
    "sdp-curation-sdk",
    "sdp-curation-service",
)

versionCatalogConf {
    artifactVersion = frameworkVersion
}



