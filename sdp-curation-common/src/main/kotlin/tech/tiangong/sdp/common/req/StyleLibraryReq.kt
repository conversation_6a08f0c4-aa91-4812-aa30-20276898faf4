package tech.tiangong.sdp.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal

/**
 * 款式库Req
 *
 * <AUTHOR>
 */
data class StyleLibraryReq(

    /**
     * 款式来源：
     * 灵感源:INSPIRATION
     * 现货选款:SPOT_SELECTION
     * 现货管理:SPOT_MANAGEMENT
     * 灵感任务分配:INSPIRATION_TASK_DISTRIBUTION
     * 款式管理:STYLE_MANAGEMENT
     */
    @field:NotBlank(message = "款式来源不能为空")
    var sourceType: String? = null,

    /**
     * 业务ID（唯一主键）
     */
    @field:NotNull(message = "业务ID不能为空")
    var busId: Long? = null,

    /**
     * 业务编码
     */
    @field:NotBlank(message = "业务编码不能为空")
    var busCode: String? = null,

    /**
     * 款式图片
     */
    @field:NotBlank(message = "款式图片不能为空")
    var styleImg: String? = null,

    /**
     * 供应商名称
     */
    var supplierName: String? = null,

    /**
     * 供应商款号
     */
    var supplierStyleCode: String? = null,

    /**
     * 品类ID
     */
    var categoryId: Long? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,

    /**
     * 销量
     */
    var soldOut: Int? = null,

    /**
     * 销售价格
     */
    var price: BigDecimal? = null,

    /**
     * 租户ID
     */
    @field:NotNull(message = "租户ID不能为空")
    var tenantId: Long? = null,
    /**
     * 创建人ID
     */
    @field:NotNull(message = "创建人ID不能为空")
    var creatorId: Long? = null,
    /**
     * 创建人名称
     */
    @field:NotBlank(message = "创建人名称不能为空")
    var creatorName: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}