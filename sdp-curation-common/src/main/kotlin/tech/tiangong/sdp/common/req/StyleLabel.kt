package tech.tiangong.sdp.common.req

import jakarta.validation.constraints.NotBlank
import java.math.BigDecimal

data class StyleLabel(

    /**
     * 建议店铺
     */
    var suggestedShopId: Long? = null,

    /**
     * 建议店铺名
     */
    var suggestedShopName: String? = null,

    /**
     * 期望价格
     */
    var expectedCostPrice: BigDecimal? = null,

    /**
     * 波次
     */
    var waveBatchCode: String? = null,


    /**
     * 品类编码
     */
    @field:NotBlank(message = "categoryCode is blank")
    var categoryCode: String? = null,

    /**
     * 品类名称
     */
    @field:NotBlank(message = "categoryName is blank")
    var categoryName: String? = null,

    /**
     * 建议风格
     */
    var suggestedStyleCode: String? = null,
    /**
     * 建议风格名称
     */
    var suggestedStyleName: String? = null,
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    var planningType: Int? = null,

    /**
     * 市场编码
     */
    var marketCode: String? = null,

    /**
     * 市场系列编码
     */
    var marketSeriesCode: String? = null,
    /**
     * 建议印花
     */
    var suggestedPrintingCode: String? = null,
    /**
     * 建议印花名称
     */
    var suggestedPrintingName: String? = null,

    /**
     * 建议国家站点编码
     */
    @field:NotBlank(message = "suggestedCountrySiteCode is blank")
    var suggestedCountrySiteCode: String? = null,


    /**
     * 货盘编号
     */
    var cargoTrayCode: String? = null,

    /**
     * 货盘名称
     */
    var cargoTrayName: String? = null,


    /**
     * 元素编码
     */
    var elementCode: String? = null,

    /**
     * 元素名称
     */
    var elementName: String? = null,

    /**
     * 场景编码
     */
    var sceneCode: String? = null,

    /**
     * 场景名称
     */
    var sceneName: String? = null,

    /**
     * 买手ID
     */
    var buyerId: Long? = null,

    /**
     * 买手名称
     */
    var buyerName: String? = null,
)
