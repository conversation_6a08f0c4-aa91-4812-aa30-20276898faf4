package tech.tiangong.sdp.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * 款式库Req
 *
 * <AUTHOR>
 */
data class StyleLibraryDeleteReq(
    /**
     * 款式来源：
     * 灵感源:INSPIRATION
     * 现货选款:SPOT_SELECTION
     * 现货管理:SPOT_MANAGEMENT
     * 灵感任务分配:INSPIRATION_TASK_DISTRIBUTION
     * 款式管理:STYLE_MANAGEMENT
     */
    @field:NotBlank(message = "款式来源不能为空")
    var sourceType: String? = null,

    /**
     * 业务ID（唯一主键）
     */
    @field:NotNull(message = "业务ID不能为空")
    var busId: Long? = null,

    /**
     * 租户ID
     */
    @field:NotNull(message = "租户ID不能为空")
    var tenantId: Long? = null,
    /**
     * 创建人ID
     */
    @field:NotNull(message = "创建人ID不能为空")
    var creatorId: Long? = null,
    /**
     * 创建人名称
     */
    @field:NotBlank(message = "创建人名称不能为空")
    var creatorName: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}