package tech.tiangong.sdp.common.req

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * 款式相似款查询
 *
 * <AUTHOR>
 */
data class StyleLibrarySimilarQuery(

    /**
     * 匹配款式来源列表（不传匹配所有来源）
     * 灵感源:INSPIRATION
     * 现货选款:SPOT_SELECTION
     * 现货管理:SPOT_MANAGEMENT
     * 灵感任务分配:INSPIRATION_TASK_DISTRIBUTION
     * 款式管理:STYLE_MANAGEMENT
     */
    var sourceTypeList: List<String>? = null,
    /**
     * 业务ID（唯一主键）用来排除本身
     */
    @field:NotNull(message = "业务ID不能为空")
    var busId: Long? = null,
    /**
     * 款式图片
     */
    @field:NotBlank(message = "款式图片不能为空")
    var styleImg: String? = null,
    /**
     * 品类名称
     */
    var categoryName: String? = null,


    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}