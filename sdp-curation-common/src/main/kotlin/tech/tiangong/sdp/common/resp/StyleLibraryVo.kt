package tech.tiangong.sdp.common.resp

import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal

/**
 * 款式库Vo
 *
 * <AUTHOR>
 */
data class StyleLibraryVo(

    /**
     * 款式来源：
     * 灵感源:INSPIRATION
     * 现货选款:SPOT_SELECTION
     * 现货管理:SPOT_MANAGEMENT
     * 灵感任务分配:INSPIRATION_TASK_DISTRIBUTION
     * 款式管理:STYLE_MANAGEMENT
     */
    var sourceType: String? = null,

    /**
     * 业务ID
     */
    var busId: Long? = null,

    /**
     * 业务编码
     */
    var busCode: String? = null,
    /**
     * 业务编码
     */
    var expandBusCode: String? = null,

    /**
     * 款式图片
     */
    var styleImg: String? = null,

    /**
     * 供应商名称
     */
    var supplierName: String? = null,

    /**
     * 供应商款号
     */
    var supplierStyleCode: String? = null,

    /**
     * 品类ID
     */
    var categoryId: Long? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,

    /**
     * 销量
     */
    var soldOut: Int? = null,

    /**
     * 销售价格
     */
    var price: BigDecimal? = null,

    /**
     * 相似程度：1-相似款；2-同款
     */
    var similarDegree: Int? = null,


    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}