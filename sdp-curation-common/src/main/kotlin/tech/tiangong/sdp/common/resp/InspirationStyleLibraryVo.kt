package tech.tiangong.sdp.common.resp

import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal

/**
 * 款式库Vo
 *
 * <AUTHOR>
 */
data class InspirationStyleLibraryVo(

    /**
     * 款式来源：
     * 灵感源:INSPIRATION
     * 现货选款:SPOT_SELECTION
     * 现货管理:SPOT_MANAGEMENT
     * 灵感任务分配:INSPIRATION_TASK_DISTRIBUTION
     * 款式管理:STYLE_MANAGEMENT
     */
    var sourceType: String? = null,


    /**
     * 业务编码
     */
    var busCode: String? = null,
    /**
     * 业务编码
     */
    var expandBusCode: String? = null,
    /**
     * 款式图片
     */
    var styleImg: String? = null,
    /**
     * 款式状态 （sourceType=INSPIRATION灵感源才有）
     *     PENDING(10, "待提交"),
     *     LOCK(20, "锁定中"),
     *     WAIT_CONFIRM(30, "待确认"),
     *     SUBMITTED(40, "已提交"),
     *     CANCEL(50, "已淘汰");
     */
    var styleStatus: Int? = null,

    /**
     * 相似程度：1-相似款；2-同款
     */
    var similarDegree: Int? = null,
    /**
     * 品类ID
     */
    var categoryId: Long? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}