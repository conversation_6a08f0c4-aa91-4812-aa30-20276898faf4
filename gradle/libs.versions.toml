[versions]
google_guava = "22.0"
easyexcel = "3.1.5"
easypoi_base = "4.3.0"
pagehelper = "5.1.11"
commons_lang3 = "3.12.0"
bfg = "1.0.0-RELEASE"
[libraries]
#google_guava = { group = "com.google.guava", name = "guava", version.ref = "google_guava" }
easyexcel = { group = "com.alibaba", name = "easyexcel", version.ref = "easyexcel" }
easypoi_base = { group = "cn.afterturn", name = "easypoi-base", version.ref = "easypoi_base" }
#pagehelper = { group = "com.github.pagehelper", name = "pagehelper", version.ref = "pagehelper" }
#commons_lang3 = { group = "org.apache.commons", name = "commons-lang3", version.ref = "commons_lang3" }
bfg-sdk = { module = "tech.tiangong.bfg:bfg-sdk", version.ref = "bfg" }