package tech.tiangong.sdp.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.common.constant.Constant
import tech.tiangong.sdp.common.req.picking.AiDesignPickingReq

/**
 * 选款相关接口
 * <AUTHOR>
 * @date 2025-1-8 16:20:34
 */
@FeignClient(
    value = Constant.APPLICATION_NAME,
    url = "\${tech.sdp-curation}",
    contextId = "PickingClient"
)
@InnerFeign
interface PickingClient {

    companion object {
        const val PATH = "${Constant.CONTEXT_PATH}/inner/v1/picking"
    }

    /**
     * AI设计创建选款
     *
     * @param req
     * @return 选款id
     */
    @PostMapping("$PATH/aigc/create")
    fun createByAiDesign(@Validated @RequestBody req: AiDesignPickingReq): DataResponse<Long>
}