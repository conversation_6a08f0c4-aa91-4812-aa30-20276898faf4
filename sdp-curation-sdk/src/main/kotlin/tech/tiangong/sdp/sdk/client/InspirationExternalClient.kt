package tech.tiangong.sdp.sdk.client

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.common.constant.Constant
import tech.tiangong.sdp.common.req.AiDesignTaskCreateReq

/**
 * 灵感相关接口
 * <AUTHOR>
 * @date 2024/12/18 20:11
 */
@FeignClient(
    value = Constant.APPLICATION_NAME,
    path = Constant.CONTEXT_PATH + "/inner/v1/inspiration",
    contextId = "InspirationExternalClient",
    url = "\${domain.ola}",
)
@InnerFeign
interface InspirationExternalClient {


    /**
     * 提交AI设计任务
     * @param req
     */
    @PostMapping("/task/submit")
    fun submitAiDesignTask(@Validated @RequestBody req: AiDesignTaskCreateReq): DataResponse<Unit>
}