{"author": "zjh", "version": "1.2.8", "userSecure": "", "currTypeMapperGroupName": "<PERSON><PERSON><PERSON>", "currTemplateGroupName": "个人的", "currColumnConfigGroupName": "<PERSON><PERSON><PERSON>", "currGlobalConfigGroupName": "<PERSON><PERSON><PERSON>", "typeMapper": {"Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"matchType": "REGEX", "columnType": "varchar(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "char(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "(tiny|medium|long)*text", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "decimal(\\(\\d+,\\d+\\))?", "javaType": "java.math.BigDecimal"}, {"matchType": "ORDINARY", "columnType": "integer", "javaType": "java.lang.Integer"}, {"matchType": "REGEX", "columnType": "(tiny|small|medium)*int(\\(\\d+\\))?", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int4", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int8", "javaType": "java.lang.Long"}, {"matchType": "REGEX", "columnType": "bigint(\\(\\d+\\))?", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "date", "javaType": "java.time.LocalDate"}, {"matchType": "ORDINARY", "columnType": "datetime", "javaType": "java.time.LocalDateTime"}, {"matchType": "ORDINARY", "columnType": "timestamp", "javaType": "java.time.LocalDateTime"}, {"matchType": "ORDINARY", "columnType": "time", "javaType": "java.time.LocalTime"}, {"matchType": "ORDINARY", "columnType": "boolean", "javaType": "java.lang.Bo<PERSON>an"}, {"matchType": "ORDINARY", "columnType": "bigint", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "bigint unsigned", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "tinyint unsigned", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "json", "javaType": "java.lang.String"}]}}, "template": {"个人的": {"name": "个人的", "elementList": [{"name": "entity.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##保存文件（宏定义）\n#save(\"/entity\", \".java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"entity\")\n\nimport com.baomidou.mybatisplus.annotation.IdType;\nimport com.baomidou.mybatisplus.annotation.TableField;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport lombok.AllArgsConstructor;\nimport lombok.Builder;\nimport lombok.Data;\nimport lombok.NoArgsConstructor;\n\n##自动导入包（全局变量）\n$!{autoImport.vm}\n\n##表注释（宏定义）\n#tableComment(\"表实体类: $tableInfo.obj.name\")\n@Data\n@Builder\n@AllArgsConstructor\n@NoArgsConstructor\n@TableName(value = \"$tableInfo.obj.name\")\npublic class $!{tableInfo.name} {\n#foreach($column in $tableInfo.pkColumn)\n    #if(${column.comment})\n    /**\n      * ${column.comment}\n      */\n    #end\n    @TableId(value = \"$!{column.obj.name}\", type = IdType.ASSIGN_ID)\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n#end\n\n#foreach($column in $tableInfo.otherColumn)\n    #if(${column.comment})\n    /**\n      * ${column.comment}\n      */\n    #end\n    @TableField(value = \"$!{column.obj.name}\")\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n#end\n}\n"}, {"name": "mapper.java.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"Mapper\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".java\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/mapper\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}mapper;\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\n\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表数据库访问层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\npublic interface $!{tableName} extends BaseMapper<$!{tableInfo.name}> {\n\n}\n"}, {"name": "mapper.xml.vm", "code": "##引入mybatis支持\n$!{mybatisSupport.vm}\n\n##设置保存名称与保存位置\n$!callback.setFileName($tool.append($!{tableInfo.name}, \"Mapper.xml\"))\n$!callback.setSavePath($tool.append($modulePath, \"/src/main/resources/mapper\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper\">\n\n    <resultMap type=\"$!{tableInfo.savePackageName}.entity.$!{tableInfo.name}\" id=\"$!{tableInfo.name}Map\">\n    <!--@mbg.generated-->\n    <!--@Table $tableInfo.obj.name-->\n        #foreach($column in $tableInfo.pkColumn)\n        <id property=\"$!column.name\" column=\"$!column.obj.name\" jdbcType=\"$!column.ext.jdbcType\"/>\n        #end\n        #foreach($column in $tableInfo.otherColumn)\n        <result property=\"$!column.name\" column=\"$!column.obj.name\" jdbcType=\"$!column.ext.jdbcType\"/>\n        #end\n    </resultMap>\n    <sql id=\"Base_Column_List\">\n    <!--@mbg.generated-->\n        #foreach($column in $tableInfo.fullColumn)\n            #if($foreach.index == 0) $!column.obj.name\n            #else ,$!column.obj.name\n            #end\n        #end\n  </sql>\n</mapper>\n"}, {"name": "entity.kt.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##保存文件（宏定义）\n#save(\"/entity\", \".kt\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"entity\")\n\nimport com.baomidou.mybatisplus.annotation.*\n\n#foreach($column in $tableInfo.otherColumn)\n    #if($!{column.obj.name} == 'deleted')\n        import com.zjkj.blade.data.mybatis.entity.BaseEntityWithNamedAndReviser\n    #end\n    #if($!{column.obj.name} == 'is_deleted')\n        import tech.tiangong.sdp.dao.BaseEntity\n    #end\n#end\n\n##自动导入包（全局变量）\n$!{autoImport.vm}\n\n##表注释（宏定义）\n#tableComment(\"表名: $tableInfo.obj.name\")\n@TableName(value = \"$tableInfo.obj.name\")\ndata class $!{tableInfo.name} (\n#foreach($column in $tableInfo.pkColumn)\n    #if(${column.comment})\n    /**\n      * ${column.comment}\n      */\n    #end\n    @TableId(value = \"$!{column.obj.name}\", type = IdType.ASSIGN_ID)\n    var $!{column.name}: $!{tool.getClsNameByFullName($column.type)}? = null,\n#end\n\n#foreach($column in $tableInfo.otherColumn)\n    #if($!{column.obj.name} != 'creator_id' \n        and $!{column.obj.name} != 'creator_name' \n        and $!{column.obj.name} != 'created_time' \n        and $!{column.obj.name} != 'reviser_id' \n        and $!{column.obj.name} != 'reviser_name' \n        and $!{column.obj.name} != 'revised_time' \n        and $!{column.obj.name} != 'is_deleted'\n        and $!{column.obj.name} != 'deleted')\n        #if(${column.comment})\n        /**\n          * ${column.comment}\n          */\n        #end\n        @TableField(value = \"$!{column.obj.name}\")\n        var $!{column.name}: #if($!{tool.getClsNameByFullName($column.type)} == 'Integer')Int#else$!{tool.getClsNameByFullName($column.type)}#end? = null,\n    #end\n#end\n) : #foreach($column in $tableInfo.otherColumn)\n        #if($!{column.obj.name} == 'deleted')\n            BaseEntityWithNamedAndReviser()\n        #end\n        #if($!{column.obj.name} == 'is_deleted')\n            BaseEntity()\n        #end\n    #end\n"}, {"name": "mapper.kt.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"Mapper\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".kt\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/mapper\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}mapper\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name}\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper\n\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表数据库访问层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\ninterface $!{tableName}: BaseMapper<$!{tableInfo.name}> {\n\n}\n"}, {"name": "repository.kt.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"Repository\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".kt\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/repository\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}repository\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper\nimport com.zjkj.blade.data.mybatis.repository.BaseRepository\nimport org.springframework.stereotype.Repository\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表数据库访问层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n@Repository\nclass $!{tableName}: BaseRepository<$!{tableInfo.name}Mapper, $!{tableInfo.name}>() {\n\n}\n"}]}}, "columnConfig": {"Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"title": "disable", "type": "BOOLEAN", "selectValue": ""}, {"title": "support", "type": "SELECT", "selectValue": "add,edit,query,del,ui"}]}}, "globalConfig": {"Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"name": "autoImport.vm", "value": "##自动导入包（仅导入实体属性需要的包，通常用于实体类）\n#foreach($import in $importList)\nimport $!import;\n#end"}, {"name": "define.vm", "value": "##（Velocity宏定义）\n\n##定义设置表名后缀的宏定义，调用方式：#setTableSuffix(\"Test\")\n#macro(setTableSuffix $suffix)\n    #set($tableName = $!tool.append($tableInfo.name, $suffix))\n#end\n\n##定义设置包名后缀的宏定义，调用方式：#setPackageSuffix(\"Test\")\n#macro(setPackageSuffix $suffix)\n#if($suffix!=\"\")package #end#if($tableInfo.savePackageName!=\"\")$!{tableInfo.savePackageName}.#{end}$!suffix;\n#end\n\n##定义直接保存路径与文件名简化的宏定义，调用方式：#save(\"/entity\", \".java\")\n#macro(save $path $fileName)\n    $!callback.setSavePath($tool.append($tableInfo.savePath, $path))\n    $!callback.setFileName($tool.append($tableInfo.name, $fileName))\n#end\n\n##定义表注释的宏定义，调用方式：#tableComment(\"注释信息\")\n#macro(tableComment $desc)\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})$desc\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n#end\n\n##定义GET，SET方法的宏定义，调用方式：#getSetMethod($column)\n#macro(getSetMethod $column)\n\n    public $!{tool.getClsNameByFullName($column.type)} get$!{tool.firstUpperCase($column.name)}() {\n        return $!{column.name};\n    }\n\n    public void set$!{tool.firstUpperCase($column.name)}($!{tool.getClsNameByFullName($column.type)} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end"}, {"name": "init.vm", "value": "##初始化区域\n\n##去掉表的t_前缀\n$!tableInfo.setName($tool.getClassName($tableInfo.obj.name.replaceFirst(\"book_\",\"\")))\n\n##参考阿里巴巴开发手册，POJO 类中布尔类型的变量，都不要加 is 前缀，否则部分框架解析会引起序列化错误\n#foreach($column in $tableInfo.fullColumn)\n#if($column.name.startsWith(\"is\") && $column.type.equals(\"java.lang.Boolean\"))\n    $!column.setName($tool.firstLowerCase($column.name.substring(2)))\n#end\n#end\n\n##实现动态排除列\n#set($temp = $tool.newHashSet(\"testCreateTime\", \"otherColumn\"))\n#foreach($item in $temp)\n    #set($newList = $tool.newArrayList())\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=$item)\n            ##带有反回值的方法调用时使用$tool.call来消除返回值\n            $tool.call($newList.add($column))\n        #end\n    #end\n    ##重新保存\n    $tableInfo.setFullColumn($newList)\n#end\n\n##对importList进行篡改\n#set($temp = $tool.newHashSet())\n#foreach($column in $tableInfo.fullColumn)\n    #if(!$column.type.startsWith(\"java.lang.\"))\n        ##带有反回值的方法调用时使用$tool.call来消除返回值\n        $tool.call($temp.add($column.type))\n    #end\n#end\n##覆盖\n#set($importList = $temp)"}, {"name": "mybatisSupport.vm", "value": "##针对Mybatis 进行支持，主要用于生成xml文件\n#foreach($column in $tableInfo.fullColumn)\n    ##储存列类型\n    $tool.call($column.ext.put(\"sqlType\", $tool.getField($column.obj.dataType, \"typeName\")))\n    #if($tool.newHashSet(\"java.lang.String\").contains($column.type))\n        #set($jdbcType=\"VARCHAR\")\n    #elseif($tool.newHashSet(\"java.lang.Boolean\", \"boolean\").contains($column.type))\n        #set($jdbcType=\"BOOLEAN\")\n    #elseif($tool.newHashSet(\"java.lang.Byte\", \"byte\").contains($column.type))\n        #set($jdbcType=\"BYTE\")\n    #elseif($tool.newHashSet(\"java.lang.Integer\", \"int\", \"java.lang.Short\", \"short\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Long\", \"long\").contains($column.type))\n        #set($jdbcType=\"BIGINT\")\n    #elseif($tool.newHashSet(\"java.lang.Float\", \"float\", \"java.lang.Double\", \"double\").contains($column.type))\n        #set($jdbcType=\"NUMERIC\")\n    #elseif($tool.newHashSet(\"java.util.Date\", \"java.sql.Timestamp\", \"java.time.Instant\", \"java.time.LocalDateTime\", \"java.time.OffsetDateTime\", \"\tjava.time.ZonedDateTime\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #elseif($tool.newHashSet(\"java.sql.Date\", \"java.time.LocalDate\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #else\n        ##其他类型\n        #set($jdbcType=\"VARCHAR\")\n    #end\n    $tool.call($column.ext.put(\"jdbcType\", $jdbcType))\n#end\n\n##定义宏，查询所有列\n#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($velocityHasNext), #end#end#end\n"}]}}}