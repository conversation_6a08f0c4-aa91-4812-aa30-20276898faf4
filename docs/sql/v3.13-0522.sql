alter table inspiration
    add column inspiration_update_time datetime null comment '灵感更新时间' after inspiration_created_time;



create table inspiration_aidc_source_history
(
    inspiration_history_id   bigint unsigned                      not null comment '灵感id'
        primary key,
    inspiration_id           bigint unsigned                      not null comment '灵感id(inspiration_aidc_source.inspiration_id)',
    third_inspiration_id     varchar(255)                         null comment '第三方灵感id',
    third_task_id            varchar(255) comment '第三方任务id',
    third_pool_id            varchar(255) comment '第三方品池策略id',
    third_inspiration_info   json                                 null comment '第三方灵感信息',
    planning_source_code     varchar(255)                         not null comment '企划来源code',
    planning_source_name     varchar(255)                         not null comment '企划来源name',
    inspiration_image_source varchar(255)                         null comment '灵感图来源',
    source_image             varchar(1000)                        not null comment '导入灵感图原图URL',
    product_link             varchar(500)                         null comment '商品链接URL',
    external_category        varchar(255)                         null comment '外部品类',
    country_site_code        varchar(255)                         null comment '来源国家站点code',
    country_site_name        varchar(255)                         null comment '来源国家站点name',
    retail_price             varchar(255)                         null comment '划线价(US)',
    sale_price               varchar(255)                         null comment '销售价(US)',
    inspiration_created_time datetime                             not null comment '灵感创建时间',
    data_source              varchar(255)                         null comment '数据来源',
    handle_status            tinyint(1) default 0                 not null comment '处理状态: 0待处理,1已处理',
    handle_message           varchar(255)                         null comment '处理信息',
    creator_id               bigint                               not null comment '创建人id',
    creator_name             varchar(32)                          not null comment '创建人名称',
    created_time             datetime                             not null comment '创建时间',
    reviser_id               bigint                               null comment '最近修改人id',
    reviser_name             varchar(32)                          null comment '最近修改人名称',
    revised_time             datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '最近更新时间',
    deleted                  tinyint(1)                           not null comment '是否已删除;0未删除，1已删除'
)
    comment '灵感数据-aidc-历史' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;


create table inspiration_history_relation
(
    relation_id   bigint unsigned                      not null comment '灵感关系id'
        primary key,
    inspiration_id           bigint unsigned                      not null comment '灵感id(inspiration.inspiration_id)',
    inspiration_history_id           bigint unsigned                      not null comment '灵感历史id(inspiration_aidc_source_history.inspiration_history_id)',
    creator_id               bigint                               not null comment '创建人id',
    creator_name             varchar(32)                          not null comment '创建人名称',
    created_time             datetime                             not null comment '创建时间',
    reviser_id               bigint                               null comment '最近修改人id',
    reviser_name             varchar(32)                          null comment '最近修改人名称',
    revised_time             datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '最近更新时间',
    deleted                  tinyint(1)                           not null comment '是否已删除;0未删除，1已删除'
)
    comment '灵感-历史关系表' collate = utf8mb4_general_ci
                                 row_format = DYNAMIC;


update inspiration
set inspiration_update_time = inspiration_created_time
where submit_status=0 and inspiration_update_time is null and inspiration_created_time is not null;