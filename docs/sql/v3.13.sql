ALTER TABLE `inspiration`
    ADD COLUMN `lock_id` bigint NULL COMMENT '锁定人id' ,
    ADD COLUMN `lock_name` varchar(32)  NULL COMMENT '锁定人名称' ,
    ADD COLUMN `cancel_code` varchar(255) NULL COMMENT '淘汰原因编码' ,
    ADD COLUMN `cancel_name` varchar(255) NULL COMMENT '淘汰原因' ,
    add similar_id          varchar(255)                       null comment '同款id',
    add imitation_param               json                               null comment '仿款选择参数',
    add column similar_style_label json comment '同款标签',
    add imitation_type  tinyint(1) null comment '仿款类型 10-内部拆版 20-现货选款',
    MODIFY COLUMN `submit_status` int NOT NULL DEFAULT 10 COMMENT '状态: 10-待提交；20-锁定中；30-待确认；40-已提交；50-已淘汰' AFTER `submit_count`,
    add column similar_average_price                    varchar(255)                       null comment '同款均价',
    add column similar_style                     json                       null comment 'tech.tiangong.sdp.common.resp.StyleLibraryVo 数组 json';


ALTER TABLE `submit_downstream_log`
    add imitation_param               json                               null comment '仿款选择参数';

--提交状态数据修数
UPDATE inspiration set submit_status = 10 where submit_status = 0;
UPDATE inspiration set submit_status = 40 where submit_status = 1;