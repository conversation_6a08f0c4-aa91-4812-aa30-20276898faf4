package tech.tiangong.sdp.service

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.holder.DefaultCurrentUserContentSetter
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.SdpApplication
import tech.tiangong.sdp.common.req.StyleLabel
import tech.tiangong.sdp.dao.repository.InspirationRepository
import tech.tiangong.sdp.req.InspirationImitationReConfirmReq
import tech.tiangong.sdp.req.inspiration.*
import java.math.BigDecimal
import java.time.LocalDateTime

@SpringBootTest(classes = [SdpApplication::class], properties = ["spring.profiles.active=uat-ola"])
@Slf4j
class InspirationServiceTest {

    private val currentUserContentSetter = DefaultCurrentUserContentSetter

    @Autowired
    private lateinit var inspirationService: InspirationService

    @Autowired
    private lateinit var inspirationRepository: InspirationRepository


    @BeforeEach
    fun setUp() =
        currentUserContentSetter.set(
            CurrentUser(
                148231653, "覃文轩", "",
                tenantId = 2L, false,
            )
        )

    @AfterEach
    fun tearDown() =
        currentUserContentSetter.clean()

    @Test
    fun imitationReConfirm() {
        val json = "{\"inspirationId\":\"7325692750115229703\",\"spuId\":\"7325766454794740623\",\"skcList\":[{\"skcId\":\"7325766454794740624\",\"skuIdList\":[]}],\"styleLabel\":{\"suggestedShopName\":\"\",\"expectedCostPrice\":22,\"waveBatchCode\":\"0901\",\"categoryCode\":\"01-0101-010101\",\"categoryName\":\"女装-上装类-衬衫\",\"suggestedStyleCode\":\"AE-Chic\",\"suggestedStyleName\":\"AE-Chic\",\"suggestedPrintingName\":\"\",\"suggestedCountrySiteCode\":\"PH\",\"cargoTrayName\":\"\",\"elementName\":\"\",\"innerCategoryCodes\":[\"01\",\"0101\",\"010101\"],\"innerCategoryNames\":[\"女装\",\"上装类\",\"衬衫\"],\"styleLabelCodes\":[\"AE\",\"Chic\"],\"styleLabelNames\":[\"AE\",\"Chic\"]}}"
//            "{\"inspirationId\":\"7325444774180536329\",\"spuId\":\"************\",\"skcList\":[{\"skcId\":\"7324334723868878346\",\"skuIdList\":[\"7324334723868878347\",\"7324334723868878348\",\"7324334723868878349\"]}],\"styleLabel\":{\"suggestedShopId\":\"7320342089300370248\",\"suggestedShopCode\":null,\"suggestedShopName\":\"VN33WH4M4S\",\"expectedCostPrice\":30.25,\"waveBatchCode\":\"0701\",\"categoryCode\":\"01>0101>010102\",\"categoryName\":\"女装>上装类>T恤\",\"suggestedStyleCode\":\"AE>Comfy\",\"suggestedStyleName\":\"AE>Comfy\",\"suggestedPrintingCode\":\"01\",\"suggestedPrintingName\":null,\"suggestedCountrySiteCode\":\"TH\",\"cargoTrayCode\":\"regular_style\",\"cargoTrayName\":\"常规款\",\"elementCode\":\"test1\",\"elementName\":\"测试-元素1\",\"innerCategoryCodes\":[\"01\",\"0101\",\"010102\"],\"innerCategoryNames\":[\"女装\",\"上装类\",\"T恤\"],\"styleLabelCodes\":[\"AE\",\"Comfy\"],\"styleLabelNames\":[\"AE\",\"Comfy\"]}}"

        val inspirationImitationReConfirmReq = InspirationImitationReConfirmReq()
        withSystemUser {
            inspirationService.imitationReConfirm(json.parseJson(InspirationImitationReConfirmReq::class.java))
        }
    }

    @Test
    fun syncSimilarLabel() {
        withSystemUser {
            val inspiration = inspirationRepository.getById(7326477724703772710)
            inspirationService.syncSimilarLabel(inspiration)
//        }

        }

    }

    @Test
    fun imitationConfirm() {
        withSystemUser {


            val json ="{\"inspirationIds\":[\"7326196014552416288\"],\"imitationType\":20,\"spuId\":\"7326198145006953936\",\"skcList\":[{\"skcId\":\"7326198145006953936\",\"skuIdList\":[]},{\"skcId\":\"7326198145006953937\",\"skuIdList\":[]}],\"styleLabel\":{\"suggestedShopId\":\"7325779076249538647\",\"suggestedShopCode\":null,\"suggestedShopName\":\"Rafiah\",\"expectedCostPrice\":60.2,\"waveBatchCode\":\"0908\",\"categoryCode\":\"01-0102-010202\",\"categoryName\":\"女装-下装类-牛仔裤\",\"suggestedStyleCode\":\"AE-Comfy\",\"suggestedStyleName\":\"AE-Comfy\",\"suggestedPrintingCode\":\"02\",\"suggestedPrintingName\":\"定位印(Positioning Mark)\",\"suggestedCountrySiteCode\":\"PH\",\"cargoTrayCode\":\"regular_style\",\"cargoTrayName\":\"常规款\",\"elementCode\":\"test2\",\"elementName\":\"测试-元素2\",\"innerCategoryCodes\":[\"01\",\"0102\",\"010202\"],\"innerCategoryNames\":[\"女装\",\"下装类\",\"牛仔裤\"],\"styleLabelCodes\":[\"AE\",\"Comfy\"],\"styleLabelNames\":[\"AE\",\"Comfy\"]}}"
//                "{\"inspirationIds\":[7326071628914610182],\"spuId\":7325766454794740623,\"skcList\":[{\"skcId\":7325766454794740624,\"skuIdList\":[]}],\"styleLabel\":{\"suggestedShopId\":7325779076249538647,\"suggestedShopCode\":null,\"suggestedShopName\":\"Rafiah\",\"expectedCostPrice\":60.25,\"waveBatchCode\":\"0901\",\"categoryCode\":\"01-0103-010301\",\"categoryName\":\"女装-裙装类-连衣裙\",\"suggestedStyleCode\":\"Lazada-VNfashion\",\"suggestedStyleName\":\"Lazada-VN fashion\",\"suggestedPrintingCode\":\"01\",\"suggestedPrintingName\":\"净色(Solid Color)\",\"suggestedCountrySiteCode\":\"US\",\"cargoTrayCode\":\"regular_style\",\"cargoTrayName\":\"常规款\",\"elementCode\":\"test1\",\"elementName\":\"测试-元素1\"},\"imitationType\":20}"
//                "{\"inspirationIds\":[\"7325446300827500583\"],\"imitationType\":20,\"spuId\":\"7324341780374315568\",\"skcList\":[],\"styleLabel\":{\"suggestedShopId\":\"7320342089300370248\",\"suggestedShopCode\":null,\"suggestedShopName\":\"VN33WH4M4S\",\"expectedCostPrice\":32.05,\"waveBatchCode\":\"0701\",\"categoryCode\":\"01>0101>010102\",\"categoryName\":\"女装>上装类>T恤\",\"suggestedStyleCode\":\"AE>Comfy\",\"suggestedStyleName\":\"AE>Comfy\",\"suggestedPrintingCode\":\"01\",\"suggestedPrintingName\":null,\"suggestedCountrySiteCode\":\"TH\",\"cargoTrayCode\":\"regular_style\",\"cargoTrayName\":\"常规款\",\"elementCode\":\"test1\",\"elementName\":\"测试-元素1\",\"innerCategoryCodes\":[\"01\",\"0101\",\"010102\"],\"innerCategoryNames\":[\"女装\",\"上装类\",\"T恤\"],\"styleLabelCodes\":[\"AE\",\"Comfy\"],\"styleLabelNames\":[\"AE\",\"Comfy\"]}}"
//            "{\"inspirationIds\":[\"7303683129876037633\"],\"spuId\":\"\",\"skcList\":[],\"styleLabel\":{\"suggestedShopId\":\"7308304887945629727\",\"suggestedShopName\":\"UNIKTEE Store\",\"expectedCostPrice\":11,\"waveBatchCode\":\"0701\",\"categoryCode\":\"01>0103>010301\",\"categoryName\":\"女装>裙装类>连衣裙\",\"suggestedStyleCode\":\"\",\"suggestedCountrySiteCode\":\"SG\",\"cargoTrayName\":\"\",\"elementName\":\"\",\"innerCategoryCodes\":[\"01\",\"0103\",\"010301\"],\"innerCategoryNames\":[\"女装\",\"裙装类\",\"连衣裙\"],\"styleLabelCodes\":[]}}"

            val inspirationImitationConfirmReq = InspirationImitationConfirmReq()
            val req1 = json.parseJson(InspirationImitationConfirmReq::class.java)
            inspirationService.imitationConfirm(req1)
        }
    }

    @Test
    fun imitationSubmit() {
        val req = InspirationImitationSubmitReq()
        req.imitationType = 10
        req.inspirationIds = setOf(7265929153097048082)
        req.spuId = 1
        val imitationProductSelectionSkcReq = ImitationProductSelectionSkcReq()
        imitationProductSelectionSkcReq.skcId = 2
        imitationProductSelectionSkcReq.skuIdList = listOf(3)
        req.skcList = listOf(imitationProductSelectionSkcReq)
        val styleLabel = StyleLabel()
        styleLabel.suggestedShopId = 1
        styleLabel.suggestedShopName = "suggestedShopCode"
        styleLabel.expectedCostPrice = BigDecimal("2.00")
        styleLabel.waveBatchCode = "suggestedShopCode"
        styleLabel.categoryCode = "suggestedShopCode"
        styleLabel.categoryName = "suggestedShopCode"
        styleLabel.suggestedStyleCode = "suggestedShopCode"
        styleLabel.suggestedPrintingCode = "suggestedShopCode"
        styleLabel.suggestedPrintingName = "suggestedShopCode"
        styleLabel.suggestedCountrySiteCode = "suggestedShopCode"
        styleLabel.cargoTrayCode = "suggestedShopCode"
        styleLabel.cargoTrayName = "suggestedShopCode"
        styleLabel.elementCode = "suggestedShopCode"
        styleLabel.elementName = "suggestedShopCode"
        req.styleLabel = styleLabel
        val json =
            "{\"inspirationIds\":[\"7325392687988187139\"],\"imitationType\":10,\"spuId\":\"\",\"skcList\":[],\"styleLabel\":{\"suggestedShopId\":\"7270688359495475213\",\"suggestedShopName\":\"乐凯大街\",\"expectedCostPrice\":11,\"waveBatchCode\":\"0901\",\"categoryCode\":\"01>0103>010301\",\"categoryName\":\"女装>裙装类>连衣裙\",\"suggestedStyleCode\":\"Lazada>VNfashion\",\"suggestedStyleName\":\"Lazada>VN fashion\",\"suggestedPrintingCode\":\"01\",\"suggestedCountrySiteCode\":\"PH\",\"cargoTrayCode\":\"regular_style\",\"cargoTrayName\":\"常规款\",\"elementCode\":\"1\",\"elementName\":\"元素1\",\"innerCategoryCodes\":[\"01\",\"0103\",\"010301\"],\"innerCategoryNames\":[\"女装\",\"裙装类\",\"连衣裙\"],\"styleLabelCodes\":[\"Lazada\",\"VNfashion\"],\"styleLabelNames\":[\"Lazada\",\"VN fashion\"]}}"

//            "{\"inspirationIds\":[\"7303683129876037633\"],\"imitationType\":10,\"spuId\":\"\",\"skcList\":[],\"styleLabel\":{\"suggestedShopId\":\"7308304887945629727\",\"suggestedShopName\":\"UNIKTEE Store\",\"expectedCostPrice\":11,\"waveBatchCode\":\"0701\",\"categoryCode\":\"01>0103>010301\",\"categoryName\":\"女装>裙装类>连衣裙\",\"suggestedStyleCode\":\"\",\"suggestedCountrySiteCode\":\"SG\",\"cargoTrayName\":\"\",\"elementName\":\"\",\"innerCategoryCodes\":[\"01\",\"0103\",\"010301\"],\"innerCategoryNames\":[\"女装\",\"裙装类\",\"连衣裙\"],\"styleLabelCodes\":[]}}"

        val req1 = json.parseJson(InspirationImitationSubmitReq::class.java)
        inspirationService.imitationSubmit(req1)
    }


    @Test
    fun page() {
        val pageReq = InspirationPageReq()
//        pageReq.externalCategory =
//        pageReq.inspirationStartCreatedTime =
//        pageReq.inspirationEndCreatedTime =
//        pageReq.suggestedSupplyModeCode =
//        pageReq.inspirationSource =
//        pageReq.sourceCountrySiteCode =
//        pageReq.identifiedResult =
//        pageReq.inspirationSubmitCount =
//        pageReq.submitStatus =
//        pageReq.creatorName =
//        pageReq.submitterName =
//        pageReq.dataSourceCode = "AIDC_ALIEXPRESS"
//        pageReq.inspirationStartUpdateTime = LocalDateTime.now()
//        pageReq.inspirationEndUpdateTime = LocalDateTime.now()
//        pageReq.planningSourceCode =
//        pageReq.creatorIds = listOf(156994597)
        pageReq.inspirationCode = "2025052708"
        pageReq.similarStyleLabels = setOf(20)
//        pageReq.waveBatchCode ="0908"

        pageReq.pageNum = 1
        pageReq.pageSize = 20
        val page = inspirationService.page(pageReq)
        log.info { "page: $page" }
    }

    @Test
    fun export() {
    }

    @Test
    fun importExcel() {
    }

    @Test
    fun importImage() {
        val json =
            "{\"supplyMethodCode\":\"Artificial\",\"countrySiteCode\":\"TH\",\"waveBatchCode\":\"0804\",\"planningSourceCode\":\"top\",\"inspirationImages\":[\"https://chuangxin-oss-cdn.tiangong.tech/tiangong_55d4dc52820e4887ab3fc3ae441d2a9f.png\"],\"inspirationImageSourceCode\":\"2222\",\"inspirationBrandCode\":\"2222\"}"
        val parseJson = json.parseJson(InspirationImportImageReq::class.java)
        val req = InspirationImportImageReq()
        withSystemUser {

            inspirationService.importImage(parseJson)
        }
    }

    @Test
    fun detail() {
        inspirationService.detail(7326539628645728257).also {
            log.info { "detail: ${it.toJson()}" }
        }
    }

    @Test
    fun taskSubmit() {
//        inspirationService.taskReSubmitDetail()
    }

    @Test
    fun taskReSubmitDetail() {
    }

    @Test
    fun getByInspirationOrPickingId() {
    }

    @Test
    fun submitAiDesignTask() {
    }

    @Test
    fun submitInspiration() {
//        val json = "{\"waveBatchCode\":\"0804\",\"supplyMethod\":\"Artificial\",\"generateMode\":1,\"generateNum\":4,\"expectedCostPrice\":0,\"modelInfo\":{\"aiModelCode\":\"\",\"aiModelName\":\"\",\"aiModelUrl\":\"\"},\"modelMaterialInfo\":{\"modelMaterialId\":\"\",\"modelMaterialName\":\"\",\"modelMaterialUrl\":\"\"},\"sceneInfo\":{\"sceneId\":\"\",\"sceneName\":\"\",\"pictureId\":\"\",\"picturePath\":\"\",\"pictureCaption\":\"\"},\"modeCode\":\"FG2_0\",\"modeName\":\"FG2.0\",\"filterBack\":1,\"faceRepair\":1,\"promiseEnhanced\":1,\"refWeight\":4,\"inspirationIds\":[\"7308304305205948704\"]}"
//        val json = "{\"inspirationId\":7303718075822669825,\"waveBatchCode\":\"0804\",\"supplyMethod\":\"Artificial\",\"generateMode\":1,\"filterBack\":0,\"faceRepair\":0,\"promiseEnhanced\":0,\"sceneInfo\":{\"sceneId\":null,\"sceneName\":\"\",\"pictureId\":null,\"picturePath\":\"\",\"pictureCaption\":\"\"},\"modelInfo\":{\"aiModelCode\":\"\",\"aiModelName\":\"\",\"aiModelUrl\":\"\"},\"modelMaterialInfo\":{\"modelMaterialId\":null,\"modelMaterialName\":\"\",\"modelMaterialUrl\":\"\"},\"generateNum\":4,\"expectedCostPrice\":0,\"categoryCode\":\"\",\"categoryName\":\"\",\"syncCategory\":1,\"modeCode\":\"FG2_0\",\"modeName\":\"FG2.0\",\"refWeight\":0,\"createExtParam\":null,\"single\":false}"
//        val req = json.parseJson(InspirationTaskSubmitReq::class.java)
//        val requests = InspirationConvert.convert(req)
//        requests.forEach{
//
//            inspirationService.submitInspiration(it)
//        }
//        val json = "{\"inspirationId\":7303718075822669825,\"waveBatchCode\":\"0804\",\"supplyMethod\":\"Artificial\",\"generateMode\":1,\"filterBack\":0,\"faceRepair\":0,\"promiseEnhanced\":0,\"sceneInfo\":{\"sceneId\":null,\"sceneName\":\"\",\"pictureId\":null,\"picturePath\":\"\",\"pictureCaption\":\"\"},\"modelInfo\":{\"aiModelCode\":\"\",\"aiModelName\":\"\",\"aiModelUrl\":\"\"},\"modelMaterialInfo\":{\"modelMaterialId\":null,\"modelMaterialName\":\"\",\"modelMaterialUrl\":\"\"},\"generateNum\":4,\"expectedCostPrice\":0,\"categoryCode\":\"\",\"categoryName\":\"\",\"syncCategory\":1,\"modeCode\":\"FG2_0\",\"modeName\":\"FG2.0\",\"refWeight\":0,\"createExtParam\":null,\"single\":false}"
        val json =
            "{\"inspirationId\":7310200300961489081,\"waveBatchCode\":\"0804\",\"supplyMethod\":\"Artificial\",\"generateMode\":1,\"filterBack\":0,\"faceRepair\":0,\"promiseEnhanced\":0,\"sceneInfo\":{\"sceneId\":null,\"sceneName\":\"\",\"pictureId\":null,\"picturePath\":\"\",\"pictureCaption\":\"\"},\"modelInfo\":{\"aiModelCode\":\"\",\"aiModelName\":\"\",\"aiModelUrl\":\"\"},\"modelMaterialInfo\":{\"modelMaterialId\":null,\"modelMaterialName\":\"\",\"modelMaterialUrl\":\"\"},\"generateNum\":4,\"expectedCostPrice\":0,\"categoryCode\":\"\",\"categoryName\":\"\",\"syncCategory\":0,\"modeCode\":\"FG2_0\",\"modeName\":\"FG2.0\",\"refWeight\":0,\"createExtParam\":null,\"single\":false}"
        val parseJson = json.parseJson(InspirationSubmitReq::class.java)
        inspirationService.submitInspiration(parseJson)
    }

    @Test
    fun remove() {
        inspirationService.remove(setOf(7270671068958978053))
    }


    @Test
    fun innerConfirm() {
        val inspiration = inspirationRepository.getById(7325430650126036998)
        inspiration.imitationParam?.let {
            val imitationParam = it.parseJson(InspirationImitationDTO::class.java)
            log.info { "imitationParam: $imitationParam" }
        }
    }

}
