package tech.tiangong.sdp.service

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.holder.DefaultCurrentUserContentSetter
import tech.tiangong.sdp.SdpApplication
import tech.tiangong.sdp.dao.repository.InspirationAidcSourceHistoryRepository

@SpringBootTest(classes = [SdpApplication::class], properties = ["spring.profiles.active=qa-ola"])
@Slf4j
class InspirationAidcServiceTest {

    private val currentUserContentSetter = DefaultCurrentUserContentSetter

    @Autowired
    private lateinit var inspirationAidcService: InspirationAidcService
    @Autowired
    private lateinit var inspirationAidcSourceHistoryRepository: InspirationAidcSourceHistoryRepository


    @BeforeEach
    fun setUp() =
        currentUserContentSetter.set(
            CurrentUser(
                151240195L, "覃文轩", "",
                tenantId = 2L, false,
            )
        )

    @AfterEach
    fun tearDown() =
        currentUserContentSetter.clean()

    @Test
    fun reflashInspiration() {
        val history = inspirationAidcSourceHistoryRepository.getById(7323655047680905378)
        inspirationAidcService.reflashInspiration(history)
    }
}