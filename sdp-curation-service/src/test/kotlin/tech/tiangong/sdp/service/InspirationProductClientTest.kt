package tech.tiangong.sdp.service

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.holder.DefaultCurrentUserContentSetter
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.SdpApplication
import tech.tiangong.sdp.common.req.StyleLibraryReq
import tech.tiangong.sdp.common.req.StyleLibrarySimilarQuery
import tech.tiangong.sdp.dao.repository.InspirationRepository
import tech.tiangong.sdp.external.CurrencyExchangeRateExternalClient
import tech.tiangong.sdp.external.InspirationProductClient
import tech.tiangong.sdp.external.StyleLibraryClient
import tech.tiangong.sdp.req.ExchangeRateQueryReq
import tech.tiangong.sdp.req.ProductSameStyleReq
import tech.tiangong.sdp.utils.UserUtils

@SpringBootTest(classes = [SdpApplication::class], properties = ["spring.profiles.active=qa-ola"])
@Slf4j
class InspirationProductClientTest {

    private val currentUserContentSetter = DefaultCurrentUserContentSetter

    @Autowired
    private lateinit var inspirationProductClient: InspirationProductClient

    @Autowired
    private lateinit var inspirationRepository: InspirationRepository


    @BeforeEach
    fun setUp() =
        currentUserContentSetter.set(
            CurrentUser(
                148231653, "覃文轩", "",
                tenantId = 2L, false,
            )
        )

    @AfterEach
    fun tearDown() =
        currentUserContentSetter.clean()

    @Test
    fun sameStyle() {
        withSystemUser{
            val imageUrl = "https://chuangxin-oss-cdn.tiangong.tech/out_7325465535272312846.png"
        inspirationProductClient.sameStyle(ProductSameStyleReq(1 ,imageUrl))
            .also {
                log.info { it.toJson() }
            }}

    }





}