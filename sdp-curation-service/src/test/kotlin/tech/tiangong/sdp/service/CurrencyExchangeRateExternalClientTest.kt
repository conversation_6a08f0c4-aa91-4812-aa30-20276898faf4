package tech.tiangong.sdp.service

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.holder.DefaultCurrentUserContentSetter
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.SdpApplication
import tech.tiangong.sdp.external.CurrencyExchangeRateExternalClient
import tech.tiangong.sdp.req.ExchangeRateQueryReq
import tech.tiangong.sdp.req.picking.PickingConfirmReq
import tech.tiangong.sdp.req.picking.PickingStylePageReq
import tech.tiangong.sdp.req.picking.PickingStyleResultPageReq

@SpringBootTest(classes = [SdpApplication::class], properties = ["spring.profiles.active=dev-ola"])
@Slf4j
class CurrencyExchangeRateExternalClientTest {

    private val currentUserContentSetter = DefaultCurrentUserContentSetter

    @Autowired
    private lateinit var currencyExchangeRateExternalClient: CurrencyExchangeRateExternalClient


    @BeforeEach
    fun setUp() =
        currentUserContentSetter.set(
            CurrentUser(
                148231653, "覃文轩", "",
                tenantId = 2L, false,
            )
        )

    @AfterEach
    fun tearDown() =
        currentUserContentSetter.clean()

    @Test
    fun queryExchangeRates() {
        val exchangeRateQueryReq = ExchangeRateQueryReq()
        exchangeRateQueryReq.currencyType = "CNY"
        currencyExchangeRateExternalClient.queryExchangeRates(exchangeRateQueryReq)
            .also {
                log.info { it.toJson() }
            }
    }


}