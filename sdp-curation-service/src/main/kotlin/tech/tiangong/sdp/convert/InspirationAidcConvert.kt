package tech.tiangong.sdp.convert

import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.amqp.EventMessage
import tech.tiangong.sdp.constants.MqConstant
import tech.tiangong.sdp.dao.entity.Inspiration
import tech.tiangong.sdp.dao.entity.InspirationAidcSource
import tech.tiangong.sdp.dao.entity.InspirationAidcSourceHistory
import tech.tiangong.sdp.req.inspiration.ThirdInspirationSaveReq
import java.time.LocalDateTime


object InspirationAidcConvert {

    @JvmStatic
    fun convert(
        inspirationAidcSourceHistory: InspirationAidcSourceHistory
    ): EventMessage = EventMessage(
        MqConstant.INSPIRATION_AIDC_UPDATE_EXCHANGE,
        MqConstant.INSPIRATION_AIDC_UPDATE_ROUTING_KEY,
        inspirationAidcSourceHistory.toJson(),
        1,
        "系统",
        1,
        LocalDateTime.now()
    )

    @JvmStatic
    fun convert(
        thirdInspiration: InspirationAidcSource,
        third: ThirdInspirationSaveReq,
    ): InspirationAidcSourceHistory {
        val inspirationInfo = InspirationAidcSourceHistory(IdHelper.getId())
        inspirationInfo.thirdPoolId = third.poolId.toString()
        inspirationInfo.thirdTaskId = third.taskId.toString()
        inspirationInfo.inspirationId = thirdInspiration.inspirationId
        inspirationInfo.thirdInspirationId = third.thirdInspirationId
        inspirationInfo.thirdInspirationInfo = third.thirdInspirationInfo
        inspirationInfo.planningSourceCode = third.planningSourceCode
        inspirationInfo.planningSourceName = third.planningSourceName
        inspirationInfo.sourceImage = third.sourceImage
        inspirationInfo.productLink = third.productLink
        inspirationInfo.externalCategory = third.externalCategory
        inspirationInfo.inspirationImageSource = third.inspirationImageSource
        inspirationInfo.countrySiteCode = third.countrySiteCode
        inspirationInfo.countrySiteName = third.countrySiteName
        inspirationInfo.retailPrice = third.retailPrice
        inspirationInfo.salePrice = third.salePrice
        inspirationInfo.inspirationCreatedTime = third.inspirationCreatedTime
        inspirationInfo.dataSource = third.dataSource
        inspirationInfo.handleStatus = 0
        return inspirationInfo
    }


    @JvmStatic
    fun convert(
        source: InspirationAidcSourceHistory,
        target: Inspiration,
    ) {
        target.thirdInspirationInfo = source.thirdInspirationInfo
        target.planningSourceCode = source.planningSourceCode
        target.planningSourceName = source.planningSourceName
        target.sourceImage = source.sourceImage
        target.productLink = source.productLink
        target.externalCategory = source.externalCategory
        target.inspirationImageSource = source.inspirationImageSource
        target.countrySiteCode = source.countrySiteCode
        target.countrySiteName = source.countrySiteName
        target.retailPrice = source.retailPrice
        target.salePrice = source.salePrice
//        target.inspirationCreatedTime = source.inspirationCreatedTime
        target.dataSource = source.dataSource
        target.dataSource = source.dataSource
        target.inspirationUpdateTime = source.inspirationCreatedTime
    }


}