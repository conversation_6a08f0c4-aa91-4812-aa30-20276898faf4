package tech.tiangong.sdp.convert


import com.alibaba.fastjson2.parseArray
import com.alibaba.fastjson2.parseObject
import org.apache.commons.lang3.StringUtils
import team.aikero.admin.common.vo.DictVo
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import team.aikero.blade.util.strings.toList
import tech.tiangong.bfg.sdk.client.FmClient
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.sdp.amqp.EventMessage
import tech.tiangong.sdp.common.req.AiDesignTaskCreateExt
import tech.tiangong.sdp.common.req.StyleLabel
import tech.tiangong.sdp.common.resp.InspirationStyleLibraryVo
import tech.tiangong.sdp.common.resp.StyleLibraryVo
import tech.tiangong.sdp.constants.MqConstant
import tech.tiangong.sdp.dao.bo.AiDesignModelBo
import tech.tiangong.sdp.dao.bo.AiDesignSceneBo
import tech.tiangong.sdp.dao.entity.AiDesignTask
import tech.tiangong.sdp.dao.entity.Inspiration
import tech.tiangong.sdp.dao.entity.InspirationAidcSourceHistory
import tech.tiangong.sdp.dao.entity.SubmitDownstreamLog
import tech.tiangong.sdp.dao.repository.AiDesignTaskRepository
import tech.tiangong.sdp.enums.*
import tech.tiangong.sdp.req.DesignDemandCreateReq
import tech.tiangong.sdp.req.ImageInfo
import tech.tiangong.sdp.req.InspirationDesignReq
import tech.tiangong.sdp.req.SmartDevelopStyleSceneReq
import tech.tiangong.sdp.req.inspiration.*
import tech.tiangong.sdp.resp.inspiration.InspirationDetailResp
import tech.tiangong.sdp.resp.inspiration.InspirationTaskSubmitResp
import tech.tiangong.sdp.resp.inspiration.TaskInfoItem
import java.time.LocalDateTime

object InspirationConvert {
    @JvmStatic
    fun convert(
        inspiration: Inspiration
    ): EventMessage = EventMessage(
        MqConstant.INSPIRATION_SYNC_SIMILAR_LABEL_EXCHANGE,
        MqConstant.INSPIRATION_SYNC_SIMILAR_LABEL_ROUTING_KEY,
        inspiration.toJson(),
        inspiration.creatorId ?: 0,
        inspiration.creatorName ?: "系统",
        1,
        LocalDateTime.now()
    )

    @JvmStatic
    fun convert(inspiration: Inspiration, req: InspirationImitationConfirmReq) {
// 只有仿款有期望成本价
        inspiration.expectedCostPrice = req.styleLabel?.expectedCostPrice
        inspiration.imitationType = req.imitationType

        // 更新波次
        inspiration.waveBatchCode = req.styleLabel?.waveBatchCode
        inspiration.submitCount = if (inspiration.submitCount == null) 1 else inspiration.submitCount!! + 1

        // 不是已提交的灵感数据, 更新状态 已提交
        if (SubmitStatusEnum.SUBMITTED.code != inspiration.submitStatus) {
            inspiration.submitStatus = SubmitStatusEnum.SUBMITTED.code
        }
        // 是否推送AIDC
        val planningSourceEnum = PlanningSourceEnum.getByCode(inspiration.planningSourceCode)
        val isPushAidc = planningSourceEnum != null
                && inspiration.submitPushAidc != YesOrNoEnum.YES.code
                && !inspiration.thirdInspirationId.isNullOrBlank()
        if (isPushAidc) {
            inspiration.submitPushAidc = YesOrNoEnum.YES.code
        }
        inspiration.lastSubmitTime = LocalDateTime.now()
        val inspirationImitationDTO = InspirationImitationDTO()
        inspirationImitationDTO.imitationType = req.imitationType
        inspirationImitationDTO.spuId = req.spuId
        inspirationImitationDTO.skcList = req.skcList?.map { skc ->
            val inspirationImitationSkcDTO = InspirationImitationSkcDTO()
            inspirationImitationSkcDTO.skcId = skc.skcId
            inspirationImitationSkcDTO.skuIdList = skc.skuIdList
            inspirationImitationSkcDTO
        }

        inspirationImitationDTO.skc1688List = req.skc1688List?.map { skc ->
            val inspirationImitation1688SkcDTO = InspirationImitation1688SkcDTO()
            inspirationImitation1688SkcDTO.skcColor = skc.skcColor
            inspirationImitation1688SkcDTO.sizeList = skc.sizeList
            inspirationImitation1688SkcDTO
        }

        inspirationImitationDTO.productPictureList = req.productPictureList

        inspirationImitationDTO.styleLabel = req.styleLabel
        inspiration.imitationParam = inspirationImitationDTO.toJson()
        inspiration.planningType = req.styleLabel?.planningType
        inspiration.marketCode = req.styleLabel?.marketCode
        inspiration.marketSeriesCode = req.styleLabel?.marketSeriesCode
        inspiration.marketStyleCode = req.styleLabel?.suggestedStyleCode
    }


    @JvmStatic
    fun convert(
        inspiration: Inspiration,
        waveBatchDictCodeMap: Map<String, DictVo>?,
        taskList: List<SubmitDownstreamLog>,
        aiDesignTaskRepository: AiDesignTaskRepository,
//        similarList: List<StyleLibraryVo>,
//        inspirations: List<Inspiration>,
    ): InspirationDetailResp {
        val detailResp = InspirationDetailResp().apply {
            this.similarAveragePrice = inspiration.similarAveragePrice
            this.imitationType = inspiration.imitationType
            this.inspirationId = inspiration.inspirationId
            this.planningSourceCode = inspiration.planningSourceCode
            this.waveBatchCode = inspiration.waveBatchCode
            this.waveBatchName = waveBatchDictCodeMap?.get(inspiration.waveBatchCode)?.dictName
            this.inspirationImage = inspiration.inspirationImage
            this.externalCategory = inspiration.externalCategory
            this.inspirationImageSource = inspiration.inspirationImageSource
            this.inspirationBrand = inspiration.inspirationBrand
            this.sourceCountrySiteName = inspiration.countrySiteName
            this.planningType = inspiration.planningType?.toString()
            this.marketCode = inspiration.marketCode

            this.marketStyleCode = inspiration.marketStyleCode
            this.marketSeriesCode = inspiration.marketSeriesCode
            this.retailPrice = inspiration.retailPrice
            this.salePrice = inspiration.salePrice
            this.suggestedSupplyModeCode = inspiration.suggestedSupplyModeCode
            this.inspirationCreatedTime = inspiration.inspirationCreatedTime
            this.dataSource = inspiration.dataSource
            this.identifiedCategory = inspiration.identifiedCategory
            this.identifiedStatus = inspiration.identifiedStatus
            this.identifiedLabel = inspiration.identifiedLabel
            this.styleType = inspiration.styleType?.let { it1 -> StyleTypeEnum.of(it1)?.desc }
            this.submitCount = inspiration.submitCount
            this.submitStatus = inspiration.submitStatus
            this.creatorName = inspiration.creatorName
            this.createdTime = inspiration.createdTime
            this.productLinkUrl = inspiration.productLink
            if (inspiration.submitStatus == SubmitStatusEnum.CANCEL.code){
                this.cancelCode = inspiration.cancelCode
                this.cancelName = inspiration.cancelName
            }

            this.taskInfo = mutableListOf()
            if (taskList.isNotEmpty()) {
                for (item in taskList) {
                    this.taskInfo!!.add(TaskInfoItem().apply {
                        this.logId = item.logId
                        this.businessId = item.businessId
                        this.businessCode = item.businessCode
                        this.waveBatchName = waveBatchDictCodeMap?.get(item.waveBatchCode)?.dictName
                        this.generationType = item.generationType
                        this.submitterName = item.creatorName
                        this.submitTime = item.createdTime
                        this.taskStatus = item.taskStatus
                        val aiTask = item.businessId?.let { aiDesignTaskRepository.getByBusinessId(it) }
                        this.aiTaskCode = aiTask?.aiTaskCode
                    })
                }
            }
        }

        inspiration.imitationParam?.let {
            detailResp.imitationReq = it.parseJson(InspirationImitationDTO::class.java)
        }
        val similarStyleLabel = inspiration.similarStyleLabel
        if (similarStyleLabel != null && similarStyleLabel != ""){
            val parseArray = similarStyleLabel.parseArray<Int>()
            detailResp.similarStyleLabels = parseArray.toSet()
        }
        inspiration.similarStyle?.let {
            detailResp.similarStyles = it.parseJsonList(InspirationStyleLibraryVo::class.java)
        }
        return detailResp
    }

    @JvmStatic
    fun convert(
        inspiration: Inspiration,
        aiDesignTask: AiDesignTask,
        taskReSubmitDetail: InspirationTaskSubmitResp,
        bizId: Long,
        createExtParam: AiDesignTaskCreateExt?
    ): InspirationSubmitReq {
        val inspirationSubmitReq = InspirationSubmitReq(
            inspiration.inspirationId ?: 0,
            taskReSubmitDetail.waveBatchCode ?: "",
            taskReSubmitDetail.supplyMethodCode ?: ""
        )
        inspirationSubmitReq.generateMode = taskReSubmitDetail.generateMode
        inspirationSubmitReq.filterBack = taskReSubmitDetail.filterBack
        inspirationSubmitReq.faceRepair = taskReSubmitDetail.faceRepair
        inspirationSubmitReq.sceneInfo = taskReSubmitDetail.sceneInfo
        inspirationSubmitReq.modelInfo = taskReSubmitDetail.modelInfo
        val modelMaterialInfoReq = ModelMaterialInfoReq()
        modelMaterialInfoReq.modelMaterialId = taskReSubmitDetail.modelMaterialInfo?.modelMaterialId
        modelMaterialInfoReq.modelMaterialName = taskReSubmitDetail.modelMaterialInfo?.modelMaterialName
        modelMaterialInfoReq.modelMaterialUrl = taskReSubmitDetail.modelMaterialInfo?.modelMaterialUrl
        inspirationSubmitReq.modelMaterialInfo = modelMaterialInfoReq
        inspirationSubmitReq.generateNum = taskReSubmitDetail.generateNum
        inspirationSubmitReq.expectedCostPrice = inspiration.expectedCostPrice
//        inspirationSubmitReq.categoryCode =
//        inspirationSubmitReq.categoryName =
//        inspirationSubmitReq.syncCategory =
        inspirationSubmitReq.modeCode = aiDesignTask.modeCode
        inspirationSubmitReq.modeName = aiDesignTask.modeName
//        inspirationSubmitReq.refWeight =
        inspirationSubmitReq.createExtParam = createExtParam
        createExtParam?.let {
            inspirationSubmitReq.generateMode = it.multiPose
            inspirationSubmitReq.filterBack = it.filterBack
            inspirationSubmitReq.promiseEnhanced = it.promiseEnhanced
            inspirationSubmitReq.faceRepair = it.faceRepair
            it.sceneInfo?.let { scene ->
                val aiDesignSceneBo = AiDesignSceneBo()
                aiDesignSceneBo.sceneId = scene.sceneId
                aiDesignSceneBo.sceneName = scene.sceneName
                aiDesignSceneBo.pictureId = scene.pictureId
                aiDesignSceneBo.picturePath = scene.picturePath
                aiDesignSceneBo.pictureCaption = scene.pictureCaption
                inspirationSubmitReq.sceneInfo = aiDesignSceneBo
            }
            it.aiModelCode?.let { aiModelCode ->
                val modelInfo = AiDesignModelBo()
                modelInfo.aiModelCode = aiModelCode
                inspirationSubmitReq.modelInfo = modelInfo
            }
            inspirationSubmitReq.generateNum = it.genCount
            inspirationSubmitReq.modeCode = it.modeCode
            inspirationSubmitReq.modeName = it.modeName
            inspirationSubmitReq.refWeight = it.refWeight
            it.parentBusId = bizId
        }
        return inspirationSubmitReq
    }

    @JvmStatic
    fun convert(req: InspirationTaskSubmitReq): List<InspirationSubmitReq> {
        return req.inspirationIds.map {
            val inspirationSubmitReq = InspirationSubmitReq(it, req.waveBatchCode, req.supplyMethod)
            inspirationSubmitReq.generateMode = req.generateMode
            inspirationSubmitReq.filterBack = req.filterBack
            inspirationSubmitReq.faceRepair = req.faceRepair
            inspirationSubmitReq.promiseEnhanced = req.promiseEnhanced
            inspirationSubmitReq.sceneInfo = req.sceneInfo
            inspirationSubmitReq.modelInfo = req.modelInfo
            inspirationSubmitReq.modelMaterialInfo = req.modelMaterialInfo
            inspirationSubmitReq.generateNum = req.generateNum
            inspirationSubmitReq.expectedCostPrice = req.expectedCostPrice
            inspirationSubmitReq.categoryCode = req.categoryCode
            inspirationSubmitReq.categoryName = req.categoryName
            inspirationSubmitReq.syncCategory = req.syncCategory
            inspirationSubmitReq.modeCode = req.modeCode
            inspirationSubmitReq.modeName = req.modeName
            inspirationSubmitReq.refWeight = req.refWeight
//            inspirationSubmitReq.styleLabel = req.styleLabel
            inspirationSubmitReq
        }.toMutableList()

    }

    @JvmStatic
    fun convert(
        inspiration: Inspiration,
        supplyMode: SupplyModeEnum,
        dictCodeMap: Map<String, DictVo>?
    ): DesignDemandCreateReq {
        val sdkReq = DesignDemandCreateReq()
        inspiration.imitationParam?.let {
            val imitationParam = it.parseJson(InspirationImitationDTO::class.java)
            sdkReq.suggestedPrintingCode = imitationParam.styleLabel?.suggestedPrintingCode
            sdkReq.suggestedPrintingName = imitationParam.styleLabel?.suggestedPrintingName
            sdkReq.elementCode = imitationParam.styleLabel?.elementCode
            sdkReq.elementName = imitationParam.styleLabel?.elementName
            sdkReq.suggestedShopId = imitationParam.styleLabel?.suggestedShopId
            sdkReq.suggestedShopName = imitationParam.styleLabel?.suggestedShopName
            sdkReq.suggestedStyleCode = imitationParam.styleLabel?.suggestedStyleCode
            sdkReq.suggestedStyle = imitationParam.styleLabel?.suggestedStyleName
            sdkReq.palletTypeCode = imitationParam.styleLabel?.cargoTrayCode
            sdkReq.palletTypeName = imitationParam.styleLabel?.cargoTrayName
            sdkReq.styleLabel =  imitationParam.styleLabel
        }
        sdkReq.planningType = sdkReq.planningType?.let { inspiration.planningType }
        sdkReq.marketCode = sdkReq.marketCode?.let { inspiration.marketCode }
        sdkReq.marketSeriesCode = sdkReq.marketSeriesCode?.let { inspiration.marketSeriesCode }
        sdkReq.sourceBizId = IdHelper.getId()
        sdkReq.inspirationStyleId = inspiration.inspirationId
        sdkReq.supplyModeName = supplyMode.desc
        sdkReq.supplyModeCode = supplyMode.code
        sdkReq.productLink = inspiration.productLink
        sdkReq.category = inspiration.identifiedCategoryCode
        sdkReq.categoryName = inspiration.identifiedCategory
        sdkReq.countrySiteCode = inspiration.countrySiteCode
        sdkReq.countrySiteName = inspiration.countrySiteName
        sdkReq.sellingPrice = inspiration.salePrice
        sdkReq.expectedCostPrice = inspiration.expectedCostPrice.toString()
        sdkReq.waveBandCode = inspiration.waveBatchCode
        sdkReq.waveBandName = dictCodeMap?.get(inspiration.waveBatchCode)?.dictName
        sdkReq.originalImage = inspiration.sourceImage
        inspiration.inspirationImage?.let {
            sdkReq.inspirationImageList = listOf(ImageInfo(it))
        }
        sdkReq.expectedCostPrice = inspiration.expectedCostPrice.toString()
        val user = CurrentUserHolder.get()
        sdkReq.submitUserName = user.name
        sdkReq.submitUserId = user.id
        sdkReq.planningSourceCode = inspiration.planningSourceCode
        sdkReq.planningSourceName = inspiration.planningSourceName
        sdkReq.inspirationImageSource = inspiration.inspirationImageSource
        sdkReq.inspirationImageSourceCode = inspiration.inspirationImageSourceCode
        sdkReq.inspirationBrandCode = inspiration.inspirationBrandCode
        sdkReq.inspirationBrand = inspiration.inspirationBrand
        sdkReq.imitationParam = inspiration.imitationParam
        return sdkReq
    }

    @JvmStatic
    fun convert(inspiration: Inspiration, req: InspirationSubmitReq): Inspiration {
        // 只有仿款有期望成本价
        inspiration.expectedCostPrice = req.expectedCostPrice

        // 更新波次
        inspiration.waveBatchCode = req.waveBatchCode
        inspiration.submitCount = if (inspiration.submitCount == null) 1 else inspiration.submitCount!! + 1

        // 不是已提交的灵感数据, 更新状态 已提交
        if (SubmitStatusEnum.SUBMITTED.code != inspiration.submitStatus) {
            inspiration.submitStatus = SubmitStatusEnum.SUBMITTED.code
        }
        // 是否推送AIDC
        val planningSourceEnum = PlanningSourceEnum.getByCode(inspiration.planningSourceCode)
        val isPushAidc = planningSourceEnum != null
                && inspiration.submitPushAidc != YesOrNoEnum.YES.code
                && !inspiration.thirdInspirationId.isNullOrBlank()
        if (isPushAidc) {
            inspiration.submitPushAidc = YesOrNoEnum.YES.code
        }
        inspiration.lastSubmitTime = LocalDateTime.now()
        if (req.syncCategory == Bool.YES.code) {
            inspiration.identifiedCategoryCode = req.categoryCode
            inspiration.identifiedCategory = req.categoryName
            // 清空识别的标签
            inspiration.identifiedLabel = "{}"
        }
        return inspiration
    }

    @JvmStatic
    fun convert(
        req: InspirationSubmitReq,
        businessCodeGenerator: BusinessCodeGenerator,
        digitalPrintTaskId: Long
    ): SubmitDownstreamLog {
        val log = SubmitDownstreamLog()
        log.logId = IdHelper.getId()
        log.inspirationId = req.inspirationId
        log.businessId = IdHelper.getId()
        log.businessCode = businessCodeGenerator.generate(CodeRuleEnum.INSPIRATION_SUBMIT_CODE)
        log.waveBatchCode = req.waveBatchCode
        log.downstreamTaskId = digitalPrintTaskId
        log.taskStatus = TaskStateEnum.SUBMIT.code
        log.generationType = SupplyModeEnum.getByCode(req.supplyMethod)?.code
        log.taskStatus = TaskStateEnum.SUBMIT.code
        log.request = req.toJson()
        return log
    }

    @JvmStatic
    fun convert(req: InspirationSubmitReq, newAiDesign: AiDesignTask): SubmitDownstreamLog {
        val log = SubmitDownstreamLog()
        log.logId = IdHelper.getId()
        log.inspirationId = newAiDesign.inspirationId
        log.businessId = newAiDesign.busId
        log.waveBatchCode = req.waveBatchCode
        log.businessCode = newAiDesign.busCode
        log.downstreamTaskId = newAiDesign.aiTaskId
        log.generationType = SupplyModeEnum.getByCode(req.supplyMethod)?.code
        log.taskStatus = TaskStateEnum.SUBMIT.code
        log.request = req.toJson()
        return log
    }

    @JvmStatic
    fun convert(aiDesignTask: AiDesignTask): InspirationDesignReq {
        val req = InspirationDesignReq()
        req.smartIdentifyId = aiDesignTask.smartIdentifyId
        req.busId = aiDesignTask.busId
        req.parentBusId = aiDesignTask.parentBusId
        req.busCode = aiDesignTask.busCode
        req.refImgUrl = aiDesignTask.inspirationImage
        req.categoryCode = aiDesignTask.categoryCode
        req.categoryName = aiDesignTask.categoryName
        req.multiPose = aiDesignTask.generateMode
        req.filterBack = aiDesignTask.filterBack
        req.faceRepair = aiDesignTask.faceRepair
        req.promiseEnhanced = aiDesignTask.promiseEnhanced
        if (aiDesignTask.sceneInfo != null && StringUtils.isNotBlank(aiDesignTask.sceneInfo)) {
            val sceneInfo = aiDesignTask.sceneInfo.parseObject<SmartDevelopStyleSceneReq>()
            if (sceneInfo != null) {
                req.sceneInfo = sceneInfo
            }
        }
        if (aiDesignTask.modelInfo != null && StringUtils.isNotBlank(aiDesignTask.modelInfo)) {
            val modelInfo = aiDesignTask.modelInfo.parseObject<AiDesignModelBo>()
            if (modelInfo != null) {
                req.aiModelCode = modelInfo.aiModelCode
            }
        }
        req.modelMaterialId = aiDesignTask.modelMaterialId
        req.modelMaterialName = aiDesignTask.modelMaterialName
        req.modelMaterialUrl = aiDesignTask.modelMaterialUrl
        req.genCount = aiDesignTask.genCount
        req.taskAttribute = 0
        req.modeCode = aiDesignTask.modeCode
        req.modeName = aiDesignTask.modeName
        req.refWeight = aiDesignTask.refWeight
        return req
    }

    @JvmStatic
    fun convert(
        req: InspirationSubmitReq,
        inspiration: Inspiration,
        businessCodeGenerator: BusinessCodeGenerator,
        fmClient: FmClient
    ): AiDesignTask {
        // 登录人信息
        val user = CurrentUserHolder.get()
        val newAiDesign = AiDesignTask()
        newAiDesign.taskId = IdHelper.getId()
        newAiDesign.busId = IdHelper.getId()
        newAiDesign.busCode = businessCodeGenerator.generate(CodeRuleEnum.INSPIRATION_SUBMIT_CODE)
        newAiDesign.taskStatus = TaskStateEnum.SUBMIT.code
        newAiDesign.inspirationId = inspiration.inspirationId
        newAiDesign.inspirationImage = inspiration.inspirationImage
        newAiDesign.smartIdentifyId = inspiration.identifiedId
        newAiDesign.categoryCode = inspiration.identifiedCategoryCode
        newAiDesign.categoryName = inspiration.identifiedCategory
        // 1.单独提交 2.aigc
        if (req.single) {
            val categoryCode = req.categoryCode
            val categoryName = req.categoryName
            newAiDesign.categoryCode = categoryCode
            newAiDesign.categoryName = categoryName
        }
        newAiDesign.syncCategory = req.syncCategory
        newAiDesign.generateMode = req.generateMode
        newAiDesign.filterBack = req.filterBack
        newAiDesign.faceRepair = req.faceRepair
        newAiDesign.promiseEnhanced = req.promiseEnhanced
        newAiDesign.planningType = inspiration.planningType
        newAiDesign.marketCode = inspiration.marketCode
        newAiDesign.marketSeriesCode = inspiration.marketSeriesCode
        newAiDesign.marketStyleCode = inspiration.marketStyleCode
        log.info { "原promiseEnhanced ${newAiDesign.promiseEnhanced}" }
        // 拿品类获取是否能履约增强的配置
        if (req.promiseEnhanced == Bool.YES.code && StringUtils.isNotBlank(newAiDesign.categoryName)) {
            val labelInfo = fmClient.getLabelInfoByName(newAiDesign.categoryName)
            log.info { "categoryName=${newAiDesign.categoryName}  labelInfo=${labelInfo.toJson()}" }
            if (labelInfo !== null && labelInfo.successful) {
                val extendLabel = labelInfo.data?.extendLabel
                if (extendLabel != null && extendLabel != "") {
                    val anyMatch = extendLabel.split(",").stream().anyMatch { it == "9" }
                    if (!anyMatch) {
                        newAiDesign.promiseEnhanced = Bool.NO.code
                    }
                }
            }
        } else {
            newAiDesign.promiseEnhanced = Bool.NO.code
        }
        log.info { "现promiseEnhanced ${newAiDesign.promiseEnhanced}" }
        newAiDesign.modelInfo = req.modelInfo?.toJson()
        newAiDesign.sceneInfo = req.sceneInfo?.toJson()
        if (req.modelMaterialInfo != null) {
            newAiDesign.modelMaterialId = req.modelMaterialInfo!!.modelMaterialId
            newAiDesign.modelMaterialName = req.modelMaterialInfo!!.modelMaterialName
            newAiDesign.modelMaterialUrl = req.modelMaterialInfo!!.modelMaterialUrl
        }
        // 修改后重试的参数重置
        req.createExtParam?.let { ext ->
            newAiDesign.categoryCode = ext.categoryCode
            newAiDesign.categoryName = ext.categoryName
            newAiDesign.modelMaterialId = ext.modelMaterialId
            newAiDesign.modelMaterialName = ext.modelMaterialName
            newAiDesign.modelMaterialUrl = ext.modelMaterialUrl
            newAiDesign.parentBusId = ext.parentBusId
        }
        newAiDesign.genCount = req.generateNum
        newAiDesign.tenantId = user.tenantId
        newAiDesign.modeCode = req.modeCode
        newAiDesign.modeName = req.modeName
        newAiDesign.refWeight = req.refWeight
        return newAiDesign
    }
}
