package tech.tiangong.sdp.convert


import com.alibaba.fastjson2.parseArray
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.common.req.UltraHdTaskCreateReq
import tech.tiangong.sdp.common.enums.YesOrNoEnum
import tech.tiangong.sdp.dao.bo.AttachmentBo
import tech.tiangong.sdp.dao.bo.PickingResultImageInfoBo
import tech.tiangong.sdp.dao.entity.*
import tech.tiangong.sdp.dao.repository.InspirationRepository
import tech.tiangong.sdp.dao.repository.PickingAiDesignPictureRepository
import tech.tiangong.sdp.dao.repository.PickingAiDesignStyleRepository
import tech.tiangong.sdp.dto.TryOnModelReferenceImageDTO
import tech.tiangong.sdp.enums.DesignSubmitStatusEnum
import tech.tiangong.sdp.enums.DictEnum
import tech.tiangong.sdp.enums.PickingStateEnum
import tech.tiangong.sdp.external.DictClientExternal
import tech.tiangong.sdp.req.DesignDemandCreateReq
import tech.tiangong.sdp.req.ImageInfo
import tech.tiangong.sdp.req.picking.PickingStyleConfirmReq
import tech.tiangong.sdp.resp.picking.PickingStyleResultDetailVo.RecommendFabricDetail
import java.time.LocalDateTime

object PickStyleConvert {
    @JvmStatic
    fun convert(
        pickingAiDesignStyle: PickingAiDesignStyle,
        reqResult: PickingStyleConfirmReq,
        dictClientExternal: DictClientExternal
    ): PickingAiDesignStyle {
        // 登录人信息
        val user = CurrentUserHolder.get()
        pickingAiDesignStyle.updateVersion = IdHelper.getId()
        pickingAiDesignStyle.pickingState = reqResult.pickingState
        pickingAiDesignStyle.selectorId = user.id
        pickingAiDesignStyle.selectorName = user.name
        pickingAiDesignStyle.selectionTime = LocalDateTime.now()
        pickingAiDesignStyle.tenantId = user.tenantId
        if (reqResult.pickingState == PickingStateEnum.NOT_SELECTED.state) {
            // 未选中, 保存跑图失败原因
            pickingAiDesignStyle.runningProblemCode = reqResult.resultDetail?.runningDiagramProblemCodes?.toJson()
        }
        if (reqResult.pickingState == PickingStateEnum.AVAILABLE.state) {
            // 已选中
            val resultDetail = reqResult.resultDetail
            if (resultDetail != null) {
                pickingAiDesignStyle.suggestedPrice = resultDetail.suggestedPrice
                pickingAiDesignStyle.suggestedStyleCode = resultDetail.suggestedStyleCode
                // 格式:a>b>c, 取最后一个 > 后面的字符
                resultDetail.suggestedStyleCode?.split(">")?.lastOrNull()?.let {
                    pickingAiDesignStyle.suggestedStyleCode = it
                    pickingAiDesignStyle.suggestedStyleName = dictClientExternal.getByDictCode(
                        DictEnum.MARKET_STYLE,
                        pickingAiDesignStyle.suggestedStyleCode!!
                    )?.dictName
                }
                // 格式:a>b>c, 取最后一个 > 后面的字符
                resultDetail.suggestedCategoryCode?.split(">")?.lastOrNull()?.let {
                    pickingAiDesignStyle.suggestedCategoryCode = it
                    pickingAiDesignStyle.suggestedCategoryName = dictClientExternal.getByDictCode(
                        DictEnum.CLOTHING_CATEGORY,
                        pickingAiDesignStyle.suggestedCategoryCode!!
                    )?.dictName
                }
                pickingAiDesignStyle.suggestedWaveBatchCode = resultDetail.suggestedWaveBatchCode
                pickingAiDesignStyle.suggestedShopId = resultDetail.suggestedShopId
                pickingAiDesignStyle.suggestedShopName = resultDetail.suggestedShopName
                pickingAiDesignStyle.suggestedShopShortCode = resultDetail.suggestedShopCode
                pickingAiDesignStyle.suggestedPrintingCode = resultDetail.suggestedPrintingCode
                pickingAiDesignStyle.suggestedCountrySiteCode = resultDetail.suggestedCountrySiteCode
                pickingAiDesignStyle.planningType = resultDetail.planningType
                pickingAiDesignStyle.marketCode = resultDetail.marketCode
                pickingAiDesignStyle.marketSeriesCode = resultDetail.marketSeriesCode
//                if (StringUtils.isNotBlank(resultDetail.suggestedCountrySiteCode)) {
//                    pickingAiDesignStyle.suggestedCountrySiteCode = resultDetail.suggestedCountrySiteCode
//                    pickingAiDesignStyle.suggestedCountrySiteName = dictClientExternal.getByDictCode(
//                        DictEnum.NATIONAL,
//                        resultDetail.suggestedCountrySiteCode!!
//                    )?.dictName
//                }
                pickingAiDesignStyle.cargoTrayCode = resultDetail.cargoTrayCode
                pickingAiDesignStyle.productThemeCode = resultDetail.productThemeCode
                pickingAiDesignStyle.productThemeName = resultDetail.productThemeName
                pickingAiDesignStyle.remark = resultDetail.remark
                pickingAiDesignStyle.attachments = resultDetail.attachments?.toJson()
                pickingAiDesignStyle.sceneCode = resultDetail.sceneCode
                pickingAiDesignStyle.sceneName = resultDetail.sceneName
                pickingAiDesignStyle.demandType = resultDetail.demandType
                pickingAiDesignStyle.modelPicList = resultDetail.modelPicList?.toJson()
                pickingAiDesignStyle.backgroundPicList = resultDetail.backgroundPicList?.toJson()
                pickingAiDesignStyle.posturePicList = resultDetail.posturePicList?.toJson()
                pickingAiDesignStyle.modelReferenceImage = resultDetail.modelReferenceImageList?.toJson()
            }
        }
        return pickingAiDesignStyle
    }

    fun convert(
        pickStylePair: MutableList<Pair<PickingAiDesignStyle, List<PickingAiDesignPicture>>>,
        pickingAiDesign: PickingAiDesign
    ): MutableList<PickingAiDesignResult> {
        val user = CurrentUserHolder.get()
        return pickStylePair.map { pair ->
            val newPickingStyle = pair.first
            val newPictureList = pair.second
            PickingAiDesignResult().apply {
                this.pickingResultId = IdHelper.getId()
                this.inspirationId = pickingAiDesign.inspirationId
                this.designTaskId = pickingAiDesign.designTaskId
                this.designTaskCode = pickingAiDesign.designTaskCode
                this.pickingState = newPickingStyle.pickingState
                if (PickingStateEnum.AVAILABLE.state == this.pickingState) {
                    this.designSubmitStatus = DesignSubmitStatusEnum.INIT.code
                }
                this.pickingId = pickingAiDesign.pickingId
                this.pickingStyleId = newPickingStyle.pickingStyleId
                this.pickingStyleSort = newPickingStyle.sort
                this.selectorId = user.id
                this.selectorName = user.name
                this.selectionTime = LocalDateTime.now()
                this.tenantId = user.tenantId
                this.suggestedPrice = newPickingStyle.suggestedPrice
                this.suggestedStyleCode = newPickingStyle.suggestedStyleCode
                this.suggestedStyleName = newPickingStyle.suggestedStyleName
                this.planningType = newPickingStyle.planningType
                this.marketCode = newPickingStyle.marketCode
                this.marketSeriesCode = newPickingStyle.marketSeriesCode
                this.suggestedCategoryCode = newPickingStyle.suggestedCategoryCode
                this.suggestedCategoryName = newPickingStyle.suggestedCategoryName
                this.suggestedWaveBatchCode = newPickingStyle.suggestedWaveBatchCode
                this.suggestedShopId = newPickingStyle.suggestedShopId
                this.suggestedShopCode = newPickingStyle.suggestedShopShortCode
                this.suggestedShopName = newPickingStyle.suggestedShopName
                this.suggestedPrintingCode = newPickingStyle.suggestedPrintingCode
                this.suggestedCountrySiteCode = newPickingStyle.suggestedCountrySiteCode
                this.suggestedCountrySiteName = newPickingStyle.suggestedCountrySiteName
                this.cargoTrayCode = newPickingStyle.cargoTrayCode
                this.attachments = newPickingStyle.attachments
                this.remark = newPickingStyle.remark
                this.pickingCreatorId = pickingAiDesign.creatorId
                this.pickingCreatorName = pickingAiDesign.creatorName
                this.pickingCreatedTime = pickingAiDesign.createdTime
                // 图片bo
                val imageBoList = newPictureList.map {
                    val imageBo = PickingResultImageInfoBo()
                    imageBo.pickingPictureId = it.pickingPictureId
                    imageBo.pictureUrl = it.pictureUrl
                    imageBo.repairImgUrl = it.repairImgUrl
                    imageBo.groupNum = it.groupNum
                    imageBo.serialNum = it.serialNum
                    imageBo.mainImageType = it.mainImageType
                    imageBo.fixImageType = it.fixImageType
                    imageBo.eliminateType = it.eliminateType
                    imageBo.eliminateReasonCodes = it.eliminateReason?.parseArray<String>()
                    imageBo
                }
                this.resultImageInfo = PickingResultImageInfoBo.boListToJson(imageBoList)
                this.sceneCode = newPickingStyle.sceneCode
                this.sceneName = newPickingStyle.sceneName
            }
        }.toMutableList()
    }


    @JvmStatic
    fun convert(results: List<PickingAiDesignResult>): List<UltraHdTask> =
        results.flatMap { pickResult ->
            val json = pickResult.resultImageInfo
            if (json == null) {
                emptyList()
            } else {
                PickingResultImageInfoBo.jsonToBoList(json).map {
                    UltraHdTask(
                        IdHelper.getId(), pickResult.tenantId ?: 0, getPictureUrl(it.pictureUrl, it.repairImgUrl),
                        it.pickingPictureId ?: 0, 0, pickResult.pickingResultId ?: 0
                    )

                }
            }
        }

    @JvmStatic
    fun convert(task: UltraHdTask, result: PickingAiDesignResult, host: String): UltraHdTaskCreateReq {
        val ultraHdTaskCreateReq = UltraHdTaskCreateReq()
        ultraHdTaskCreateReq.busId = task.taskId
        ultraHdTaskCreateReq.taskMode = "SMART_DESIGN"
        ultraHdTaskCreateReq.pictureUrl = task.imageUrl
        ultraHdTaskCreateReq.callback = "$host/sdp-curation/inner/v1/picking/callback/ultra-hd-task"
        ultraHdTaskCreateReq.tenantId = result.tenantId
        ultraHdTaskCreateReq.creatorId = result.selectorId
        ultraHdTaskCreateReq.creatorName = result.selectorName
        return ultraHdTaskCreateReq
    }

    @JvmStatic
    fun convert(newTask: UltraHdTask,sourceTask: UltraHdTask, host: String): UltraHdTaskCreateReq {
        val ultraHdTaskCreateReq = UltraHdTaskCreateReq()
        ultraHdTaskCreateReq.busId = newTask.taskId
        ultraHdTaskCreateReq.taskMode = "SMART_DESIGN"
        ultraHdTaskCreateReq.pictureUrl = newTask.imageUrl
        ultraHdTaskCreateReq.callback = "$host/sdp-curation/inner/v1/picking/callback/ultra-hd-task"
        ultraHdTaskCreateReq.tenantId = sourceTask.tenantId
        ultraHdTaskCreateReq.creatorId = sourceTask.creatorId
        ultraHdTaskCreateReq.creatorName = sourceTask.creatorName
        return ultraHdTaskCreateReq
    }


    /**
     * 优先使用修复图
     *
     * @param pictureUrl
     * @param repairImgUrl
     * @return
     */
    @JvmStatic
    private fun getPictureUrl(pictureUrl: String?, repairImgUrl: String?): String {
        return if (!repairImgUrl.isNullOrBlank()) {
            repairImgUrl
        } else {
            pictureUrl ?: ""
        }
    }


    @JvmStatic
    fun convert(
        pickingAiDesignResult: PickingAiDesignResult,
        pickingAiDesign: PickingAiDesign,
        dictClientExternal: DictClientExternal,
        aiDesignTask: AiDesignTask?,
        fabrics: List<RecommendFabricDetail>?,
        inspirationRepository: InspirationRepository,
        pickingAiDesignPictureRepository: PickingAiDesignPictureRepository,
        pickingAiDesignStyleRepository: PickingAiDesignStyleRepository
    ): DesignDemandCreateReq {
        val sdkReq = DesignDemandCreateReq()
        sdkReq.sourceBizId = pickingAiDesignResult.pickingResultId
        sdkReq.inspirationStyleId = pickingAiDesignResult.pickingResultId
        sdkReq.supplyModeName = pickingAiDesign.supplyMethodName
        sdkReq.supplyModeCode = pickingAiDesign.supplyMethodCode
        sdkReq.productLink = pickingAiDesign.productLink
        sdkReq.category = pickingAiDesignResult.suggestedCategoryCode
        sdkReq.categoryName = pickingAiDesignResult.suggestedCategoryName
        sdkReq.suggestedStyle = pickingAiDesignResult.suggestedStyleName
        sdkReq.suggestedStyleCode = pickingAiDesignResult.suggestedStyleCode
        sdkReq.planningType = pickingAiDesignResult.planningType
        sdkReq.marketCode = pickingAiDesignResult.marketCode
        sdkReq.marketSeriesCode = pickingAiDesignResult.marketSeriesCode
        sdkReq.countrySiteCode = pickingAiDesignResult.suggestedCountrySiteCode
        sdkReq.countrySiteName = pickingAiDesignResult.suggestedCountrySiteName
        sdkReq.storeId = pickingAiDesignResult.suggestedShopId
        sdkReq.storeName = pickingAiDesignResult.suggestedShopName
        sdkReq.sellingPrice = pickingAiDesignResult.suggestedPrice?.toString()
        sdkReq.waveBandCode = pickingAiDesignResult.suggestedWaveBatchCode
        sdkReq.waveBandName = pickingAiDesignResult.suggestedWaveBatchCode
        sdkReq.chosenId = pickingAiDesignResult.selectorId
        sdkReq.chosenName = pickingAiDesignResult.selectorName
        sdkReq.chosenTime = pickingAiDesignResult.selectionTime
        sdkReq.originalImage = pickingAiDesign.inspirationImage
        sdkReq.palletTypeCode = pickingAiDesignResult.cargoTrayCode
        sdkReq.palletTypeName = pickingAiDesignResult.cargoTrayCode?.let { it1 ->
            dictClientExternal.getByDictCode(
                DictEnum.TRAY_TYPE,
                it1
            )?.dictName
        }
        sdkReq.aigcRemark = pickingAiDesignResult.remark
        val attachments = pickingAiDesignResult.attachments
        if (StringUtils.isNotBlank(attachments)) {
            sdkReq.attachments = attachments.parseArray<AttachmentBo>()
        }
        if (aiDesignTask != null) {
            sdkReq.runNo = aiDesignTask.aiTaskCode
            sdkReq.runCreatorName = aiDesignTask.creatorName
            sdkReq.submitUserId = aiDesignTask.creatorId
            sdkReq.submitUserName = aiDesignTask.creatorName
        }
        sdkReq.inspirationImageList = convert(pickingAiDesignResult.resultImageInfo?: "[]",pickingAiDesignPictureRepository)

        if (CollectionUtils.isEmpty(sdkReq.inspirationImageList)) {
            throw BusinessException("当前没有可用结果图")
        }
        if (!fabrics.isNullOrEmpty()) {
            var index = 0
            val list = fabrics
                .filter { ff -> ff.commodityId != null }
                .map {
                    val fabric = DesignDemandCreateReq.SuggestedMaterialInfo()
                    fabric.sortNum = index
                    fabric.commodityName = it.commodityName
                    fabric.spuId = it.commodityId
                    fabric.spuCode = it.commodityCode
                    fabric.skuId = it.skuId
                    fabric.skuCode = it.skuCode
                    index++
                    fabric
                }
            sdkReq.suggestedMaterialList = list
        }
        sdkReq.sceneCode = pickingAiDesignResult.sceneCode
        sdkReq.sceneName = pickingAiDesignResult.sceneName
        val inspirationId = pickingAiDesignResult.inspirationId
        if (inspirationId != null) {
            val inspiration = inspirationRepository.getById(inspirationId)
            if (inspiration != null) {
                sdkReq.planningSourceCode = inspiration.planningSourceCode
                sdkReq.planningSourceName = inspiration.planningSourceName
                sdkReq.inspirationImageSource = inspiration.inspirationImageSource
                sdkReq.inspirationImageSourceCode = inspiration.inspirationImageSourceCode
                sdkReq.inspirationBrandCode = inspiration.inspirationBrandCode
                sdkReq.inspirationBrand = inspiration.inspirationBrand
            }

        }
        // 商品主题
        pickingAiDesignResult.pickingStyleId?.let { it ->
            pickingAiDesignStyleRepository.getById(it)?.let { style->
                sdkReq.productThemeCode = style.productThemeCode
                sdkReq.productThemeName = style.productThemeName
                //视觉需求
                sdkReq.demandType = style.demandType
                sdkReq.modelAttachments = AttachmentBo.jsonToBoList(style.modelPicList)
                sdkReq.backgroundAttachments = AttachmentBo.jsonToBoList(style.backgroundPicList)
                sdkReq.postureAttachments = AttachmentBo.jsonToBoList(style.posturePicList)
                style.modelReferenceImage?.let {str ->
                    sdkReq.modelReferenceImageList = str.parseJsonList(TryOnModelReferenceImageDTO::class.java)
                }

            }
        }
        return sdkReq
    }


    @JvmStatic
    fun convert(resultImageInfo: String,pickingAiDesignPictureRepository: PickingAiDesignPictureRepository): List<ImageInfo> {
        val imageIds = PickingResultImageInfoBo.jsonToBoList(resultImageInfo)
            .filter { YesOrNoEnum.NO.code == it.eliminateType }
            // 优先主图第一位:  mainImageType 降序排序，如果 mainImageType 相同，则按 serialNum 升序排序
            .map { it.pickingPictureId }
        if (imageIds.isEmpty()){
            return emptyList()
        }
        val images = pickingAiDesignPictureRepository.listByIds(imageIds)
        if (images.isEmpty()) {
            return emptyList()
        }
        return images.map {
            ImageInfo(it.pictureUrl?: "")
                .apply {
                    ultraHdUrl = it.ultraHdPictureUrl
                    mainImage = it.mainImageType
                }
        }
    }
}