package tech.tiangong.sdp.constants

/**
 * <AUTHOR>
 * @date 2024/12/5 10:43
 */
class MqConstant {

    companion object {
        // 灵感任务淘汰
        const val DESIGN_DEMAND_ELIMINATE_E = "e.sdp_design.design_demand_no_pass"
        const val DESIGN_DEMAND_ELIMINATE_R = "r.sdp_design.design_demand_no_pass"
        const val DESIGN_DEMAND_ELIMINATE_Q = "q.sdp_design.design_demand_no_pass"

        // 灵感任务开款
        const val DESIGN_DEMAND_CREATE_SPU_E = "e.sdp_design.design_demand_create_spu"
        const val DESIGN_DEMAND_CREATE_SPU_R = "r.sdp_design.design_demand_create_spu"
        const val DESIGN_DEMAND_CREATE_SPU_Q = "q.sdp_design.design_demand_create_spu"


        // 灵感更新
        const val INSPIRATION_AIDC_UPDATE_EXCHANGE = "e.sdp_curation.inspiration_aidc_update"
        const val INSPIRATION_AIDC_UPDATE_ROUTING_KEY = "r.sdp_curation.inspiration_aidc_update"
        const val INSPIRATION_AIDC_UPDATE_QUEUE = "q.sdp_curation.inspiration_aidc_update"


        // 同步同步款式标签
        const val INSPIRATION_SYNC_SIMILAR_LABEL_EXCHANGE = "e.sdp_curation.sync.similar.label"
        const val INSPIRATION_SYNC_SIMILAR_LABEL_ROUTING_KEY = "r.sdp_curation.sync.similar.label"
        const val INSPIRATION_SYNC_SIMILAR_LABEL_QUEUE = "q.sdp_curation.sync.similar.label"
    }
}