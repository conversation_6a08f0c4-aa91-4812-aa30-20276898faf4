package tech.tiangong.sdp.external

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.common.req.StyleLibraryDeleteReq
import tech.tiangong.sdp.common.req.StyleLibraryReq
import tech.tiangong.sdp.common.req.StyleLibrarySimilarQuery
import tech.tiangong.sdp.common.resp.StyleLibraryVo

/**
 * 款式库Client
 */
@FeignClient(
    contextId = "styleLibraryClient",
    value = "inspiration-service",
    url = "\${domain.fashion-api}",
    path = "/inspiration/inner/style-library",
)
interface StyleLibraryClient {
    /**
     * 相似款列表查询
     *
     * @param query StyleLibrarySimilarQuery
     * @return List<StyleLibraryVo>
     */
    @PostMapping("/find/similar/list")
    fun findSimilarList(@RequestBody query: StyleLibrarySimilarQuery): DataResponse<List<StyleLibraryVo>>

    /**
     * 新建款式库信息
     *
     * @param req StyleLibraryReq
     * @return 款式库ID
     */
    @PostMapping("/create")
    fun create(@RequestBody @Validated req: StyleLibraryReq): DataResponse<Long>

    /**
     * 更新款式库信息
     *
     * @param req StyleLibraryReq
     * @return 款式库ID
     */
    @PostMapping("/update")
    fun update(@RequestBody @Validated req: StyleLibraryReq): DataResponse<Long>

    /**
     * 删除款式库信息
     *
     * @param req StyleLibraryDeleteReq
     * @return 删除的款式库ID
     */
    @DeleteMapping("/delete")
    fun delete(@RequestBody @Validated req: StyleLibraryDeleteReq): DataResponse<Long>
}