package tech.tiangong.sdp.external

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.InnerApi
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.req.ExchangeRateQueryReq
import tech.tiangong.sdp.resp.ExchangeRateResp

@FeignClient(
    value = "pop-product-service",
    path = "/pop-product-service/inner/v1/exchange-rates",
    contextId = "currencyExchangeRateExternalClient",
    url = "\${domain.nest-api}"
)
@InnerApi
interface CurrencyExchangeRateExternalClient {
    /**
     * 查询汇率列表
     */
    @PostMapping("/query")
    fun queryExchangeRates(@RequestBody @Validated req: ExchangeRateQueryReq): DataResponse<List<ExchangeRateResp>>
}