package tech.tiangong.sdp.external

import jakarta.validation.Valid
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.req.ProductSameStyleReq
import tech.tiangong.sdp.req.inspiration.Product1688SelectionStyleReq
import tech.tiangong.sdp.req.inspiration.ProductSelectionStyleReq
import tech.tiangong.sdp.resp.ProductSameStyleVo
import tech.tiangong.sdp.resp.StyleSelectionAddResp

/**
 * 灵感-商品池Client
 */
@FeignClient(
    contextId = "inspirationProductClient",
    value = "inspiration-service",
    url = "\${domain.nest-api}",
    path = "/inspiration/inner/product",
)
interface InspirationProductClient {

    /**
     * 添加到选款
     *
     * @param req ProductAddToSelectionStyleReq
     * @return 响应结果
     */
    @PostMapping(value = ["/add-to/selection-style"])
    fun selection(@Validated @RequestBody req: ProductSelectionStyleReq): DataResponse<StyleSelectionAddResp>


    /**
     * 同款货源（商品查询）
     *
     * @param req ProductAddToSelectionStyleReq
     * @return 响应结果
     */
    @PostMapping("/same-style")
    fun sameStyle(@RequestBody @Valid req: ProductSameStyleReq): DataResponse<List<ProductSameStyleVo>>



    /**
     * 1688图搜添加到选款
     *
     * @param req Product1688SelectionStyleReq
     * @return 响应结果
     */
    @PostMapping("/1688/add-to/selection-style")
    fun add1688selection(@RequestBody @Valid req: Product1688SelectionStyleReq): DataResponse<StyleSelectionAddResp>


}