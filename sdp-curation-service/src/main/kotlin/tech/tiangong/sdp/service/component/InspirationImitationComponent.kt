package tech.tiangong.sdp.service.component

import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.sdp.convert.InspirationConvert
import tech.tiangong.sdp.dao.entity.Inspiration
import tech.tiangong.sdp.dao.entity.SubmitDownstreamLog
import tech.tiangong.sdp.dao.repository.InspirationRepository
import tech.tiangong.sdp.dao.repository.SubmitDownstreamLogRepository
import tech.tiangong.sdp.enums.*
import tech.tiangong.sdp.external.InspirationProductClient
import tech.tiangong.sdp.req.inspiration.InspirationImitationConfirmReq
import tech.tiangong.sdp.req.inspiration.Product1688SelectionStyleReq
import tech.tiangong.sdp.req.inspiration.ProductSelectionStyleReq
import tech.tiangong.sdp.req.inspiration.ProductSelectionStyleReq.ProductSelectionSkcReq
import tech.tiangong.sdp.resp.StyleSelectionAddResp


@Slf4j
@Component
class InspirationImitationComponent(
    private val inspirationRepository: InspirationRepository,
    private val inspirationProductClient: InspirationProductClient,
    private val inspirationTaskComponent: InspirationTaskComponent,
    private val lazadaComponent: LazadaComponent,
    private val submitDownstreamLogRepository: SubmitDownstreamLogRepository,
//    private val businessCodeGenerator: BusinessCodeGenerator,
) {

    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    fun innerSubmit(inspiration: Inspiration, req: InspirationImitationConfirmReq,successes:MutableSet<Long>) {
        req.imitationType = 10
        InspirationConvert.convert(inspiration, req)
        val sameStyleStartTime = System.nanoTime()
        val demandId = inspirationTaskComponent.submitDesignTask(inspiration, SupplyModeEnum.OBM_REPLICA)
        log.info { "submitDesignTask 执行时间: ${(System.nanoTime() - sameStyleStartTime) / 1_000_000} 毫秒\"" }
        inspirationRepository.updateById(inspiration)
        // 是否推送AIDC
        val planningSourceEnum = PlanningSourceEnum.getByCode(inspiration.planningSourceCode)
        val isPushAidc =
            planningSourceEnum != null && inspiration.submitPushAidc != YesOrNoEnum.YES.code && !inspiration.thirdInspirationId.isNullOrBlank()
        // 调用AIDC推送
        if (isPushAidc && planningSourceEnum != null) {
            lazadaComponent.pushAidcMark(planningSourceEnum, inspiration)
        }
        if (demandId != null) {
            successes.add(demandId)
        }

    }


    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    fun selectionSubmit(inspiration: Inspiration, req: InspirationImitationConfirmReq,successes:MutableSet<Long>) {
        req.imitationType = 20
        InspirationConvert.convert(inspiration, req)
        log.info { "更新灵感 = ${inspiration.toJson()}" }
        inspirationRepository.updateById(inspiration)

        var selectionResp : DataResponse<StyleSelectionAddResp>?= null
        var lastProcessedReq: Any? = null  // 记录最后处理的请求对象

        if(!req.skcList.isNullOrEmpty()){
            // 现货选款
            val productSelectionStyleReq = ProductSelectionStyleReq()
            productSelectionStyleReq.supplyModeCode= SupplyModeEnum.OBM_REPLICA.code
            productSelectionStyleReq.supplyModeName = SupplyModeEnum.OBM_REPLICA.desc
            productSelectionStyleReq.styleLabel = req.styleLabel
            productSelectionStyleReq.inspirationId = inspiration.inspirationId
            productSelectionStyleReq.spuId = req.spuId
            productSelectionStyleReq.productPictureList = req.productPictureList
            productSelectionStyleReq.skcList = req.skcList?.map {
                val productSelectionSkcReq = ProductSelectionSkcReq()
                productSelectionSkcReq.skcId = it.skcId
                productSelectionSkcReq.skuIdList = it.skuIdList
                productSelectionSkcReq
            }

            lastProcessedReq = productSelectionStyleReq
            log.info { "productSelectionStyleReq = ${productSelectionStyleReq.toJson()}" }
            val sameStyleStartTime = System.nanoTime()
            selectionResp = inspirationProductClient.selection(productSelectionStyleReq)
            log.info { "selection 执行时间: ${(System.nanoTime() - sameStyleStartTime) / 1_000_000} 毫秒\"" }
            log.info { "selectionResp = ${selectionResp!!.toJson()}" }
            if (selectionResp.code != 200 || !selectionResp.successful) {
                log.error{ "提交现货选款失败 ${selectionResp!!.toJson()}" }
                throw BusinessException(selectionResp.message)
            }
        }


        if(!req.skc1688List.isNullOrEmpty()){
            // 现货选款 1688图搜出来的款
            val product1688SelectionStyleReq = Product1688SelectionStyleReq()
            product1688SelectionStyleReq.supplyModeCode= SupplyModeEnum.OBM_REPLICA.code
            product1688SelectionStyleReq.supplyModeName = SupplyModeEnum.OBM_REPLICA.desc
            product1688SelectionStyleReq.styleLabel = req.styleLabel
            product1688SelectionStyleReq.inspirationId = inspiration.inspirationId
            product1688SelectionStyleReq.spuId = req.spuId
            product1688SelectionStyleReq.productPictureList = req.productPictureList
            product1688SelectionStyleReq.skcList = req.skc1688List?.map {
                val product1688SelectionSkcReq = Product1688SelectionStyleReq.Product1688SelectionSkcReq()
                product1688SelectionSkcReq.skcColor = it.skcColor
                product1688SelectionSkcReq.sizeList = it.sizeList
                product1688SelectionSkcReq
            }
            lastProcessedReq = product1688SelectionStyleReq
            log.info { "product1688SelectionStyleReq = ${product1688SelectionStyleReq.toJson()}" }
            val sameStyleStartTime = System.nanoTime()
            selectionResp = inspirationProductClient.add1688selection(product1688SelectionStyleReq)
            log.info { "add1688selection 执行时间: ${(System.nanoTime() - sameStyleStartTime) / 1_000_000} 毫秒\"" }
            log.info { "add1688selection = ${selectionResp.toJson()}" }
            if (selectionResp.code != 200 || !selectionResp.successful) {
                log.error{ "1688图搜的款提交现货选款失败 ${selectionResp.toJson()}" }
                throw BusinessException(selectionResp.message)
            }
        }


        // 新增一个新的记录
        val submitLog = SubmitDownstreamLog()
        submitLog.logId = IdHelper.getId()
        submitLog.inspirationId = inspiration.inspirationId
        submitLog.businessId = IdHelper.getId()
        submitLog.businessCode = selectionResp!!.data?.styleSelectionSpuCode
        submitLog.waveBatchCode = inspiration.waveBatchCode
        submitLog.downstreamTaskId = selectionResp.data?.styleSelectionSpuId
        submitLog.taskStatus = TaskStateEnum.SUBMIT.code
        submitLog.generationType = SupplyModeEnum.OBM_REPLICA.code
        submitLog.request = lastProcessedReq!!.toJson()
        submitLog.response = selectionResp.toJson()
        submitLog.imitationParam = inspiration.imitationParam
        submitDownstreamLogRepository.save(submitLog)


        selectionResp.data?.styleSelectionSpuId?.let { successes.add(it) }
        inspirationRepository.getById(inspiration.inspirationId).also {
            log.info { "更新灵感之后查询结果 = ${it.toJson()}" }
        }

    }

}