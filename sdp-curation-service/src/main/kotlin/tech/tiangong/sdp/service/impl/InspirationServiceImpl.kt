package tech.tiangong.sdp.service.impl

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import com.alibaba.fastjson2.parseArray
import com.baomidou.mybatisplus.extension.kotlin.KtUpdateWrapper
import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.auth.util.UserContextUtil
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.bfg.sdk.client.FmClient
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.sdp.amqp.RabbitProducer
import tech.tiangong.sdp.common.req.AiDesignTaskCreateReq
import tech.tiangong.sdp.common.req.StyleLibraryDeleteReq
import tech.tiangong.sdp.common.req.StyleLibraryReq
import tech.tiangong.sdp.common.req.StyleLibrarySimilarQuery
import tech.tiangong.sdp.common.resp.GetInspirationOrPickingIdResp
import tech.tiangong.sdp.common.resp.GetInspirationOrPickingIdResp.InspirationInfoResp
import tech.tiangong.sdp.common.resp.GetInspirationOrPickingIdResp.PickingInfoResp
import tech.tiangong.sdp.common.resp.InspirationResp
import tech.tiangong.sdp.common.resp.InspirationStyleLibraryVo
import tech.tiangong.sdp.common.resp.StyleLibraryVo
import tech.tiangong.sdp.convert.InspirationConvert
import tech.tiangong.sdp.dao.bo.AiDesignModelBo
import tech.tiangong.sdp.dao.bo.AiDesignSceneBo
import tech.tiangong.sdp.dao.bo.KeyValueBo
import tech.tiangong.sdp.dao.entity.Inspiration
import tech.tiangong.sdp.dao.entity.InspirationLabel
import tech.tiangong.sdp.dao.entity.SubmitDownstreamLog
import tech.tiangong.sdp.dao.repository.*
import tech.tiangong.sdp.dto.InspirationExportDTO
import tech.tiangong.sdp.dto.InspirationImportDTO
import tech.tiangong.sdp.enums.*
import tech.tiangong.sdp.external.CurrencyExchangeRateExternalClient
import tech.tiangong.sdp.external.DictClientExternal
import tech.tiangong.sdp.external.InspirationProductClient
import tech.tiangong.sdp.external.StyleLibraryClient
import tech.tiangong.sdp.req.ExchangeRateQueryReq
import tech.tiangong.sdp.req.InspirationImitationReConfirmReq
import tech.tiangong.sdp.req.ProductSameStyleReq
import tech.tiangong.sdp.req.inspiration.*
import tech.tiangong.sdp.req.inspiration.ProductSelectionStyleReq.ProductSelectionSkcReq
import tech.tiangong.sdp.resp.ProductSameStyleVo
import tech.tiangong.sdp.resp.inspiration.*
import tech.tiangong.sdp.service.InspirationService
import tech.tiangong.sdp.service.component.InspirationImageComponent
import tech.tiangong.sdp.service.component.InspirationImitationComponent
import tech.tiangong.sdp.service.component.InspirationTaskComponent
import tech.tiangong.sdp.service.component.LazadaComponent
import tech.tiangong.sdp.utils.FileExportUtils
import tech.tiangong.sdp.utils.TransactionHelper
import tech.tiangong.sdp.utils.UserUtils
import java.io.IOException
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime

/**
 * 灵感源
 * <AUTHOR>
 * @date 2024/11/20 09:55
 */
@Service
@Slf4j
class InspirationServiceImpl(
    private val inspirationRepository: InspirationRepository,
    private val inspirationLabelRepository: InspirationLabelRepository,
    private val submitDownstreamLogRepository: SubmitDownstreamLogRepository,
    private val aiDesignTaskRepository: AiDesignTaskRepository,
    private val pickingAiDesignResultRepository: PickingAiDesignResultRepository,
    private val inspirationTaskComponent: InspirationTaskComponent,
    private val dictClientExternal: DictClientExternal,
    private val lazadaComponent: LazadaComponent,
    private val businessCodeGenerator: BusinessCodeGenerator,
    private val transactionManager: PlatformTransactionManager,
    private val inspirationImageComponent: InspirationImageComponent,
    private val fmClient: FmClient,
    private val styleLibraryClient: StyleLibraryClient,
    private val inspirationHistoryRelationRepository: InspirationHistoryRelationRepository,
    private val inspirationAidcSourceHistoryRepository: InspirationAidcSourceHistoryRepository,
    private val inspirationImitationComponent: InspirationImitationComponent,
    private val inspirationProductClient: InspirationProductClient,
    private val currencyExchangeRateExternalClient: CurrencyExchangeRateExternalClient,
    private val rabbitProducer: RabbitProducer,
    private val imageHandlerExecutor: ThreadPoolTaskExecutor,

    ) : InspirationService {

    /**
     * 列表分页
     * @param req 请求对象
     * @return
     */
    override fun page(req: InspirationPageReq): PageVo<InspirationPageResp> {
        val pageData = inspirationRepository.listPage(req)
        if (pageData.records.isEmpty() || pageData.records.isEmpty()) {
            return PageVo(req.pageNum, 0, listOf())
        }
        val inspirationIds = pageData.records.mapNotNull { it.inspirationId }
        var labelMap = mapOf<Long, List<InspirationLabel>>()
        if (inspirationIds.isNotEmpty()) {
            labelMap = inspirationLabelRepository.getLabelsByInspirationIds(inspirationIds)
        }

        // key=字典值code,value=字典值vo
        val dictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.PLM_CLOTHING_BAND)?.children?.associateBy({ it.dictCode },
                { it })

        return PageVo(req.pageNum, pageData.total.toInt(), pageData.records.map {
            val resp = InspirationPageResp()
            resp.similarAveragePrice = it.similarAveragePrice
            resp.inspirationUpdateTime = it.inspirationUpdateTime
            resp.inspirationId = it.inspirationId
            resp.inspirationCode = it.inspirationCode
            resp.planningSourceCode = it.planningSourceCode
            resp.planningSourceName = it.planningSourceName
            resp.waveBatchCode = it.waveBatchCode
            resp.waveBatchName = it.waveBatchCode?.let { it1 -> dictCodeMap?.get(it1)?.dictName }
            resp.inspirationImage = it.inspirationImage
            resp.externalCategory = it.externalCategory
            resp.inspirationImageSource = it.inspirationImageSource
            resp.sourceCountrySiteName = it.countrySiteName
            resp.retailPrice = it.retailPrice
            resp.salePrice = it.salePrice
            resp.suggestedSupplyModeCode = it.suggestedSupplyModeCode
            resp.inspirationCreatedTime = it.createdTime
            resp.dataSource = it.dataSource
            resp.identifiedCategory = it.identifiedCategory
            resp.identifiedStatus = it.identifiedStatus
            val labelKvList = mutableListOf<KeyValueBo>()
            labelMap[it.inspirationId]?.forEach { label ->
                val kv = KeyValueBo()
                kv.key = label.labelName
                kv.value = label.labelValueName
                labelKvList.add(kv)
            }
            resp.identifiedLabel = labelKvList
            resp.styleType = it.styleType?.let { it1 -> StyleTypeEnum.of(it1)?.desc }
            resp.submitCount = it.submitCount
            resp.submitStatus = it.submitStatus
            resp.creatorId = it.creatorId
            resp.creatorName = it.creatorName
            resp.inspirationBrand = it.inspirationBrand
            resp.lockId = it.lockId
            resp.lockName = it.lockName
            val similarStyleLabel = it.similarStyleLabel
            if (similarStyleLabel != null && similarStyleLabel != "") {
                val parseArray = similarStyleLabel.parseArray<Int>()
                resp.similarStyleLabels = parseArray.toSet()
            }
            resp
        })
    }

    /**
     * 导出
     * @param req 请求对象
     * @return
     */
    override fun export(response: HttpServletResponse, req: InspirationPageReq) {
        req.pageNum = 1
        req.pageSize = 500
        val allData: MutableList<InspirationExportDTO> = mutableListOf()
        val marketDictVo = dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE)
        // 市场(一级节点)
        val marketMap = marketDictVo?.children?.associateBy({ it.dictCode },
            { it.dictName })
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketDictVo?.children
            ?.flatMap { parent ->
                parent.children.orEmpty().map { child ->
                    child.dictCode to child.dictName
                }
            }
            ?.toMap()
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketDictVo?.children
            ?.flatMap { parent ->
                parent.children.orEmpty().flatMap { child ->
                    child.children.orEmpty().map { style ->
                        style.dictCode to style.dictName
                    }
                }
            }
            ?.toMap()

        while (true) {
            val pageData = inspirationRepository.listPage(req)
            if (pageData.records.isEmpty() || pageData.records.isEmpty()) {
                break
            }

            req.pageNum += 1

            val inspirationIds = pageData.records.mapNotNull { it.inspirationId }
            var labelMap = mapOf<Long, List<InspirationLabel>>()
            if (inspirationIds.isNotEmpty()) {
                labelMap = inspirationLabelRepository.getLabelsByInspirationIds(inspirationIds)
            }

            pageData.records.forEach {
                val dto = InspirationExportDTO()
                dto.planningSource = it.planningSourceName
                dto.waveBatchCode = it.waveBatchCode
                dto.inspirationImage = it.inspirationImage
                dto.externalCategory = it.externalCategory
                dto.inspirationImageSource = it.inspirationImageSource
                dto.sourceCountrySiteName = it.countrySiteName
                dto.retailPrice = it.retailPrice
                dto.salePrice = it.salePrice
                dto.suggestedSupplyMethod = SupplyModeEnum.getByCode(it.suggestedSupplyModeCode)?.desc
                dto.inspirationCreatedTime = it.inspirationCreatedTime
                dto.dataSource = it.dataSource
                dto.productLink = it.productLink
                dto.identifiedCategory = it.identifiedCategory
                dto.identifiedStatus = it.identifiedStatus?.let { it1 -> IdentifiedStatusEnum.getByCode(it1)?.desc }
                dto.styleType = it.styleType?.let { it1 -> StyleTypeEnum.of(it1)?.desc }
                dto.submitCount = it.submitCount
                dto.submitStatus = it.submitStatus?.let { it1 -> SubmitStatusEnum.getByCode(it1)?.desc }
                dto.marketName = it.marketCode?.let { marketMap?.get(it) }
                dto.marketSeriesName = it.marketSeriesCode?.let { marketSeriesMap?.get(it) }
                dto.marketStyleName = it.marketStyleCode?.let { marketStyleMap?.get(it) }
                // 仿款单独再取
                it.imitationParam?.let {
                    val imitationParam = it.parseJson(InspirationImitationDTO::class.java)
                    // TODO 优先取suggestedStyleName 没有再取字典，维护好字典可统一取字典
                    dto.marketStyleName = imitationParam.styleLabel?.suggestedStyleName.takeIf { it.isNullOrBlank().not() }
                        ?: imitationParam.styleLabel?.suggestedStyleCode?.let { marketStyleMap?.get(it) }
                }
                dto.planningTypeName = it.planningType?.let { it1 -> PlanningTypeEnum.getByCode(it1)?.desc}

                //  labelMap[it.inspirationId] 组装成字符串, 格式:key:value,key1:value1
                val labelKvMap =
                    labelMap[it.inspirationId]?.associateBy({ it1 -> it1.labelName }, { it2 -> it2.labelValueName })
                val labelList = labelKvMap?.entries?.map { it1 -> "${it1.key}:${it1.value}" }
                dto.identifiedLabel = labelList?.joinToString(",")
                allData.add(dto)
            }
        }/*
        InspirationExportDTO
        导出文件名称为模块+日期+批次
        如导出灵感源2024102601
         */
        val fileName = "灵感源" + businessCodeGenerator.generate(CodeRuleEnum.INSPIRATION_EXPORT) + ".xlsx"
        try {
            FileExportUtils.exportExcelEntity(fileName, response, InspirationExportDTO::class.java, allData)
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }

    /**
     * Excel导入
     * @param file
     */
    override fun importExcel(file: MultipartFile): InspirationImportResultVo {
        val result = InspirationImportResultVo()
        EasyExcel.read(file.inputStream,
            InspirationImportDTO::class.java,
            object : AnalysisEventListener<InspirationImportDTO>() {
                private val cachedDataList: MutableList<InspirationImportDTO> = mutableListOf()

                override fun invoke(data: InspirationImportDTO, context: AnalysisContext) {
                    // 在这里处理每一行数据，例如打印出来
                    var isError = false

                    // 抽出公共逻辑
                    fun checkField(check: Boolean, errorMessage: String) {
                        if (check) {
                            val failureDetail = InspirationImportResultVo.FailureDetail()
                            failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                            failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                            result.failureDetails.add(failureDetail)
                            isError = true
                        }
                    }
                    // 校验
                    checkField(StringUtils.isBlank(data.suggestedSupplyMethod), "第 %s 行的”供给方式“不能为空！")
                    checkField(StringUtils.isBlank(data.planningSource), "第 %s 行的”企划来源“不能为空！")
                    checkField(StringUtils.isBlank(data.waveBatchCode), "第 %s 行的”波次“不能为空！")
                    checkField(StringUtils.isBlank(data.sourceImage), "第 %s 行的”图片URL“不能为空！")
                    // 判断灵感图来源和灵感图品牌
                    val inspirationImageSource = data.inspirationImageSource
                    if (StringUtils.isNotBlank(inspirationImageSource)) {
                        val find =
                            dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_IMAGE_SOURCE)?.children?.find { dictVo -> dictVo.dictName == inspirationImageSource }
                        if (find == null) {
                            checkField(true, "第 %s 行的”灵感图来源“未找到！")
                        }
                    }
                    val inspirationBrand = data.inspirationBrand
                    if (StringUtils.isNotBlank(inspirationBrand)) {
                        val find =
                            dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_BRAND)?.children?.find { dictVo -> dictVo.dictName == inspirationBrand }
                        if (find == null) {
                            checkField(true, "第 %s 行的”灵感图品牌“未找到！")
                        }
                    }
                    if (isError) {
                        result.failCount += 1
                        return
                    }
                    cachedDataList.add(data)
                    result.successCount += 1
                }

                override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                    // 保存数据
                    saveData()
                }

                private fun saveData() {
                    if (CollectionUtils.isEmpty(cachedDataList)) {
                        log.warn { "没有数据，不进行存储" }
                        return
                    }

                    // key=字典值name,value=字典值vo
                    val planningSourceNameMap =
                        dictClientExternal.getTopByDictCode(DictEnum.PLANNING_SOURCE)?.children?.associateBy({ it.dictName },
                            { it })
                    // key=字典值code,value=字典值vo
                    val countryNameMap =
                        dictClientExternal.getTopByDictCode(DictEnum.NATIONAL)?.children?.associateBy({ it.dictName },
                            { it })

                    val inspirationImageSource =
                        dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_IMAGE_SOURCE)?.children?.associateBy({ it.dictName },
                            { it })
                    val inspirationBrand =
                        dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_BRAND)?.children?.associateBy({ it.dictName },
                            { it })
                    //  市场国家要取二级节点，重复取第一个
//                    val nationalMarketNameMap =
//                        dictClientExternal.getTopByDictCode(DictEnum.NATIONAL_MARKET)
//                            ?.children
//                            ?.flatMap { it.children.orEmpty() }
//                            ?.groupBy({ it.dictName }, { it.dictCode })
//                            ?.mapValues { it.value.first() }
                    // 市场(一级节点)
                    val marketMap =
                        dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE)?.children?.associateBy({ it.dictName },
                            { it })
                    // 系列(二级节点) 市场-系列
                    val marketSeriesMap =
                        dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE)
                            ?.children
                            ?.flatMap { parent ->
                                parent.children.orEmpty().map { child ->
                                    "${parent.dictName}-${child.dictName}" to child.dictCode
                                }
                            }
                            ?.toMap()
                    // 风格(三级节点) 市场-系列-风格
                    val marketStyleMap =
                        dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE)
                            ?.children
                            ?.flatMap { parent ->
                                parent.children.orEmpty().flatMap { child ->
                                    child.children.orEmpty().map { series ->
                                        val key = "${parent.dictName}-${child.dictName}-${series.dictName}"
                                        key to series.dictCode
                                    }
                                }
                            }
                            ?.toMap()

                    // 20一批
                    cachedDataList.chunked(20).map { batch ->
                        val saveData = batch.map {
                            val inspiration = Inspiration()
                            inspiration.inspirationId = IdHelper.getId()
                            inspiration.inspirationCode =
                                businessCodeGenerator.generate(CodeRuleEnum.INSPIRATION_EXPORT)
                            inspiration.planningSourceCode =
                                it.planningSource?.let { it1 -> planningSourceNameMap?.get(it1)?.dictCode }
                            inspiration.planningSourceName = it.planningSource
                            inspiration.waveBatchCode = it.waveBatchCode
                            inspiration.sourceImage = it.sourceImage
                            inspiration.inspirationImage = it.sourceImage
                            inspiration.productLink = it.productLink
                            inspiration.externalCategory = it.externalCategory
                            inspiration.inspirationImageSource = it.inspirationImageSource
                            inspiration.inspirationImageSourceCode =
                                it.inspirationImageSource?.let { it1 -> inspirationImageSource?.get(it1)?.dictCode }
                            inspiration.inspirationBrand = it.inspirationBrand
                            inspiration.inspirationBrandCode =
                                it.inspirationBrand?.let { it1 -> inspirationBrand?.get(it1)?.dictCode }
                            inspiration.countrySiteCode =
                                it.sourceCountrySiteName?.let { it1 -> countryNameMap?.get(it1)?.dictCode }
                            inspiration.countrySiteName = it.sourceCountrySiteName
                            inspiration.retailPrice = it.retailPrice?.toString()
                            inspiration.salePrice = it.salePrice?.toString()
                            inspiration.suggestedSupplyModeCode =
                                SupplyModeEnum.getByDesc(it.suggestedSupplyMethod)?.code.toString()
                            inspiration.suggestedSupplyModeName =
                                SupplyModeEnum.getByDesc(it.suggestedSupplyMethod)?.desc.toString()
                            inspiration.inspirationCreatedTime = LocalDateTime.now()
                            inspiration.dataSource = InspirationDataSourceTypeEnum.IMPORT.content
                            inspiration.submitCount = 0
                            inspiration.submitStatus = SubmitStatusEnum.PENDING.code
                            inspiration.planningType = PlanningTypeEnum.getCodeByDesc(it.planningTypeName)
                            inspiration.marketCode =
                                it.marketName?.let { it1 -> marketMap?.get(it1)?.dictCode }
                            inspiration.marketSeriesCode =
                                it.marketSeriesName?.let { it1 ->
                                    val key = "${it.marketName}-$it1"
                                    marketSeriesMap?.get(key)
                                }
                            inspiration.marketStyleCode =
                                it.marketStyleName?.let { it1 ->
                                    val key = "${it.marketName}-${it.marketSeriesName}-$it1"
                                    marketStyleMap?.get( key)
                                }
                            inspiration
                        }
                        // 手动事务
                        TransactionTemplate(transactionManager).executeWithoutResult {
                            inspirationRepository.saveBatch(saveData)
                            TransactionHelper.afterCommitExecute {
                                // 异步执行
//                                Thread {
                                // 开始处理
                                inspirationImageComponent.handler(saveData.mapNotNull { it.inspirationId })
//                                }.start()
                            }
                        }
                    }
                    log.info { "导入图片-${cachedDataList.size}条数据" }
                }
            }).sheet().doRead()
        return result
    }

    /**
     * 图片导入
     * @param req
     */
    override fun importImage(req: InspirationImportImageReq) {
        // key=字典值code,value=字典值vo
        val planningSourceDictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.PLANNING_SOURCE)?.children?.associateBy({ it.dictCode },
                { it })
        // key=字典值code,value=字典值vo
        val countryDictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.NATIONAL)?.children?.associateBy({ it.dictCode }, { it })
        val inspirationImageSourceCode = req.inspirationImageSourceCode
        if (StringUtils.isNotBlank(inspirationImageSourceCode)) {
            val find =
                dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_IMAGE_SOURCE)?.children?.find { it.dictCode == inspirationImageSourceCode }
            if (find == null) {
                throw BusinessException("灵感图来源未找到！")
            }
        }
        val inspirationBrandCode = req.inspirationBrandCode
        if (StringUtils.isNotBlank(inspirationBrandCode)) {
            val find =
                dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_BRAND)?.children?.find { it.dictCode == inspirationBrandCode }
            if (find == null) {
                throw BusinessException("灵感图品牌未找到！")
            }
        }


        // 灵感图来源
        val inspirationSource =
            dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_IMAGE_SOURCE)?.children?.associateBy({ it.dictCode },
                { it })

        // 灵感图品牌
        val inspirationBrand =
            dictClientExternal.getTopByDictCode(DictEnum.INSPIRATION_BRAND)?.children?.associateBy({ it.dictCode },
                { it })
        val inspirationList = req.inspirationImages?.map {
            val inspiration = Inspiration()
            inspiration.inspirationId = IdHelper.getId()
            inspiration.inspirationCode = businessCodeGenerator.generate(CodeRuleEnum.INSPIRATION_EXPORT)
            inspiration.planningSourceCode = req.planningSourceCode
            inspiration.planningSourceName =
                req.planningSourceCode?.let { it1 -> planningSourceDictCodeMap?.get(it1)?.dictName }
            inspiration.waveBatchCode = req.waveBatchCode
            inspiration.inspirationImage = it
            inspiration.sourceImage = it
            inspiration.inspirationImageSourceCode = req.inspirationImageSourceCode
            inspiration.inspirationImageSource =
                req.inspirationImageSourceCode?.let { it1 -> inspirationSource?.get(it1)?.dictName }
            inspiration.inspirationBrand = req.inspirationBrandCode?.let { it1 -> inspirationBrand?.get(it1)?.dictName }
            inspiration.inspirationBrandCode = req.inspirationBrandCode
            inspiration.countrySiteCode = req.countrySiteCode
            inspiration.countrySiteName = req.countrySiteCode?.let { it1 -> countryDictCodeMap?.get(it1)?.dictName }
            inspiration.suggestedSupplyModeCode = req.supplyMethodCode
            inspiration.suggestedSupplyModeName = SupplyModeEnum.getByCode(req.supplyMethodCode)?.desc
            inspiration.inspirationCreatedTime = LocalDateTime.now()
            inspiration.dataSource = InspirationDataSourceTypeEnum.IMPORT.content
            inspiration.submitCount = 0
            inspiration.submitStatus = SubmitStatusEnum.PENDING.code
            inspiration.marketCode = req.marketCode
            inspiration.marketStyleCode = req.marketStyleCode
            inspiration.marketSeriesCode = req.marketSeriesCode
            inspiration.planningType = req.planningType
            inspiration
        }
        if (!inspirationList.isNullOrEmpty()) {
            inspirationRepository.saveBatch(inspirationList)
            TransactionHelper.afterCommitExecute {
                // 异步执行
//                Thread {

                // 开始处理
                inspirationImageComponent.handler(inspirationList.mapNotNull { it.inspirationId })


//                }.start()
            }
        }
    }

    /**
     * 详情
     * @param inspirationId
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun detail(inspirationId: Long): InspirationDetailResp {
        val inspiration: Inspiration =
            inspirationRepository.getById(inspirationId) ?: throw BusinessException("灵感源数据不存在")
        val taskList: List<SubmitDownstreamLog> = submitDownstreamLogRepository.listByInspirationId(inspirationId)

        // key=字典值code,value=字典值vo
        val waveBatchDictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.PLM_CLOTHING_BAND)?.children?.associateBy({ it.dictCode },
                { it })
        val marketName = inspiration.marketCode?.let { it1 ->
            dictClientExternal.getByDictCode(
                DictEnum.MARKET_STYLE, it1
            )?.dictName
        }
        val marketStyleName = inspiration.marketStyleCode?.let { it1 ->
            dictClientExternal.getByDictCode(
                DictEnum.MARKET_STYLE, it1
            )?.dictName
        }
        val marketSeriesName = inspiration.marketSeriesCode?.let { it1 ->
            dictClientExternal.getByDictCode(
                DictEnum.MARKET_STYLE, it1
            )?.dictName
        }
        val apply = InspirationConvert.convert(
            inspiration, waveBatchDictCodeMap, taskList, aiDesignTaskRepository
        )
        apply.marketName = marketName
        apply.marketStyleName = marketStyleName
        if(marketStyleName.isNullOrBlank()){
            // 仿款单独再取
            inspiration.imitationParam?.let {
                val imitationParam = it.parseJson(InspirationImitationDTO::class.java)
                apply.marketStyleName = imitationParam.styleLabel?.suggestedStyleName
            }
        }
        apply.marketSeriesName = marketSeriesName
        // 查更新记录
        val relations = inspirationHistoryRelationRepository.listByInspirationId(inspirationId)
        if (relations.isNotEmpty()) {
            val historyIds = relations.map { it.inspirationHistoryId }
            val updateLogs = inspirationAidcSourceHistoryRepository.listByIds(historyIds).map {
                val updateLog = UpdateLog()
                updateLog.taskId = it.thirdTaskId
                updateLog.poolId = it.thirdPoolId
                updateLog.updateTime = it.inspirationCreatedTime
                updateLog
            }.sortedByDescending { it.updateTime }
            apply.updateLogs = updateLogs
        }
        try {
            val productSameStyleReq = ProductSameStyleReq(inspirationId, inspiration.inspirationImage ?: "")
            log.info { "查询同款货源 req inspirationId=${inspiration.inspirationId} productSameStyleReq=${productSameStyleReq.toJson()}" }
            val sameStyleStartTime = System.nanoTime()
            val sameStyleResp = inspirationProductClient.sameStyle(productSameStyleReq)
            log.info { "sameStyle 执行时间: ${(System.nanoTime() - sameStyleStartTime) / 1_000_000} 毫秒\"" }
            log.info { "查询同款货源 sameStyleResp =${sameStyleResp.toJson()}" }
            if (sameStyleResp.successful || sameStyleResp.code == 200) {
                if (sameStyleResp.data?.isNotEmpty() == true) {
                    apply.productSameStyles = sameStyleResp.data
                }
            }
            // 异步去更新标签
            rabbitProducer.send(InspirationConvert.convert(inspiration))
        } catch (e: Exception) {
            log.error(e) { "查询同款货源异常 ${e.message}"}
        }
        return apply


    }

    /**
     * 重新提交-页面回显
     * @param businessId
     */
    override fun taskReSubmitDetail(businessId: Long): InspirationTaskSubmitResp {
        // 获取已有的log
        val log = submitDownstreamLogRepository.getByBusinessId(businessId) ?: throw BusinessException("记录不存在")
        val inspiration = inspirationRepository.getById(log.inspirationId)

        return InspirationTaskSubmitResp().apply {
            // 通用字段
            this.inspirationId = log.inspirationId
            this.waveBatchCode = log.waveBatchCode
            this.supplyMethodCode = log.generationType
            // 特殊字段
            val oldAiDesign = aiDesignTaskRepository.getByBusinessId(businessId)
            if (oldAiDesign != null) {
                this.generateMode = oldAiDesign.generateMode
                this.sceneInfo = oldAiDesign.sceneInfo?.parseJson(AiDesignSceneBo::class.java)
                this.modelInfo = oldAiDesign.modelInfo?.parseJson(AiDesignModelBo::class.java)
                this.generateNum = oldAiDesign.genCount
                this.filterBack = oldAiDesign.filterBack
                this.faceRepair = oldAiDesign.faceRepair
                this.modelMaterialInfo = ModelMaterialInfoResp().apply {
                    this.modelMaterialId = oldAiDesign.modelMaterialId
                    this.modelMaterialName = oldAiDesign.modelMaterialName
                    this.modelMaterialUrl = oldAiDesign.modelMaterialUrl
                }
                this.modeCode = oldAiDesign.modeCode
                this.modeName = oldAiDesign.modeName

            }
            if (log.imitationParam != null && log.imitationParam != "") {
                this.imitationReq = log.imitationParam?.parseJson(InspirationImitationDTO::class.java)
            }

        }
    }

    /**
     * 灵感id/选款id获取相关信息
     * @param inspirationPickingId
     * @return
     */
    override fun getByInspirationOrPickingId(inspirationPickingId: Long): GetInspirationOrPickingIdResp {
        /*
            setSourceBizId 灵感>现货: submit_downstream_log.business_id
            setSourceBizId 选款>AIGC picking_ai_design_result.picking_result_id
            inspirationId 数码印花: inspiration.inspiration_id
         */
        var inspirationId: Long? = inspirationPickingId
        val submitDownstreamLog = submitDownstreamLogRepository.getByBusinessId(inspirationPickingId)
        if (submitDownstreamLog != null) {
            inspirationId = submitDownstreamLog.inspirationId
        }
        val result = pickingAiDesignResultRepository.getById(inspirationPickingId)
        if (result != null) {
            inspirationId = result.inspirationId
        }

        var inspiration: Inspiration? = null
        if (inspirationId != null) {
            inspiration = inspirationRepository.getById(inspirationId)
        }

        /*
        选款 存在, 灵感 存在
        选款 不存在, 灵感 存在
        选款 存在, 灵感 不存在
         */
        return GetInspirationOrPickingIdResp().apply {
            if (inspiration != null) {
                this.inspirationInfo = InspirationInfoResp().apply {
                    this.inspirationImage = inspiration.inspirationImage
                    this.inspirationImageSource = inspiration.inspirationImageSource
                    this.countrySiteCode = inspiration.countrySiteCode
                    this.countrySiteName = inspiration.countrySiteName
                    this.planningSourceCode = inspiration.planningSourceCode
                    this.planningSourceName = inspiration.planningSourceName
                    if (PlanningSourceEnum.TOP.dictCode == inspiration.planningSourceCode) {
                        this.retailPriceRange = inspiration.retailPrice
                        this.salePriceRange = inspiration.salePrice
                    } else {
                        this.retailPrice = inspiration.retailPrice?.toBigDecimal()
                        this.salePrice = inspiration.salePrice?.toBigDecimal()
                    }
                }
            }
            this.pickingInfo = PickingInfoResp().apply {
                if (result != null) {
                    this.countrySiteCode = result.suggestedCountrySiteCode
                    this.countrySiteName = result.suggestedCountrySiteName
                    this.shopId = result.suggestedShopId
                    this.shopCode = result.suggestedShopCode
                    this.shopName = result.suggestedShopName
                    this.remark = result.remark
                    this.cargoTrayCode = result.cargoTrayCode
                    this.cargoTrayName = result.cargoTrayCode?.let { it1 ->
                        dictClientExternal.getByDictCode(
                            DictEnum.TRAY_TYPE, it1
                        )?.dictName
                    }
                    this.suggestedPrice = result.suggestedPrice
                    this.pickingTime = result.createdTime
                }
            }
        }
    }

    override fun listByIds(idList: List<Long>): List<InspirationResp> {
        if (idList.isEmpty()) return emptyList()
        return inspirationRepository.listByIds(idList)
            .map { InspirationResp()
                .apply {
                    inspirationId = it.inspirationId
                    inspirationImageSource = it.inspirationImageSource
                    productLink = it.productLink
                } }
    }

    /**
     * 提交AI设计任务
     * @param req
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun submitAiDesignTask(req: AiDesignTaskCreateReq) {
        val bizId = req.bizId
        val aiDesignTask =
            aiDesignTaskRepository.getByBusinessId(bizId) ?: throw BusinessException("AI设计任务不存在, busId: $bizId")
        val inspirationId = aiDesignTask.inspirationId
        val inspiration = inspirationRepository.getById(inspirationId)
        val taskReSubmitDetail = taskReSubmitDetail(bizId)
        val createExtParam = req.createExtParam
        val inspirationTaskSubmitReq: InspirationSubmitReq =
            InspirationConvert.convert(inspiration, aiDesignTask, taskReSubmitDetail, bizId, createExtParam)
        // 品类和素材传递进去吧
        log.info { "submitAiDesignTask converted req:${inspirationTaskSubmitReq.toJson()}" }
        submitInspiration(inspirationTaskSubmitReq)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun remove(inspirationIds: Set<Long>) {
        val inspirations = inspirationRepository.listByIds(inspirationIds) ?: throw BusinessException("数据不存在")
        val user = CurrentUserHolder.get()
        val find = inspirations.find { it ->
            val notYours = (it.creatorId != user.id) || (it.submitStatus != SubmitStatusEnum.PENDING.code)
            notYours
        }
        if (find != null) {
            throw BusinessException("数据不属于当前用户")
        }
        inspirationRepository.removeByIds(inspirationIds)
        inspirations.forEach { inspiration ->
            val styleLibraryReq = StyleLibraryDeleteReq()
            styleLibraryReq.sourceType = "INSPIRATION"
            styleLibraryReq.busId = inspiration.inspirationId
            styleLibraryReq.tenantId = 1
            styleLibraryReq.creatorId = inspiration.creatorId
            styleLibraryReq.creatorName = inspiration.creatorName
            log.info { "灵感删除相似度styleLibraryReq=${styleLibraryReq.toJson()}" }
            val deleteResp = styleLibraryClient.delete(styleLibraryReq)
            log.info { "灵感删除相似度deleteResp=${deleteResp.toJson()}" }
        }

    }

    /**
     * 提交任务
     * @param req
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun submitInspiration(req: InspirationSubmitReq) {
        verifySubmitReq(req)
        val inspiration = inspirationRepository.getById(req.inspirationId)
        // 更新灵感
        InspirationConvert.convert(inspiration, req)
        // 调用款式开发平台创建任务
        when (val supplyModeEnum = SupplyModeEnum.getByCode(req.supplyMethod)) {
            SupplyModeEnum.AIGC -> {
                // 新的AI设计任务
                val newAiDesign = InspirationConvert.convert(req, inspiration, businessCodeGenerator, fmClient)
                // 调用款式开发平台创建任务
                inspirationTaskComponent.submitAiDesignTask(newAiDesign)
                // 新增一个新的记录
                val log = InspirationConvert.convert(req, newAiDesign)
                submitDownstreamLogRepository.save(log)
            }

            SupplyModeEnum.LOGO_NUM -> {
                // 调用数码印花
                val digitalPrintTaskId = inspirationTaskComponent.submitDigitalPrintingTask(inspiration)
                // 新增一个新的记录
                val log = InspirationConvert.convert(req, businessCodeGenerator, digitalPrintTaskId ?: 0)
                submitDownstreamLogRepository.save(log)
            }

//            SupplyModeEnum.OBM_REPLICA -> {
//                inspiration.imitationType = 10
//                inspiration.imitationParam = req.styleLabel?.toJson()
//                inspirationTaskComponent.submitDesignTask(inspiration, supplyModeEnum)
//            }

            else -> {
                if (supplyModeEnum != null) {
                    inspirationTaskComponent.submitDesignTask(inspiration, supplyModeEnum)
                }
            }
        }
        inspirationRepository.updateById(inspiration)
        // 是否推送AIDC
        val planningSourceEnum = PlanningSourceEnum.getByCode(inspiration.planningSourceCode)
        val isPushAidc =
            planningSourceEnum != null && inspiration.submitPushAidc != YesOrNoEnum.YES.code && !inspiration.thirdInspirationId.isNullOrBlank()
        // 调用AIDC推送
        if (isPushAidc && planningSourceEnum != null) {
            lazadaComponent.pushAidcMark(planningSourceEnum, inspiration)
        }
    }

    override fun lockOrRelease(req: InspirationLockReq) {
        var list = inspirationRepository.listByIds(req.inspirationIds)
        if (CollectionUtils.isEmpty(list) || req.inspirationIds.size != list.size) {
            throw BusinessException("灵感id不存在，请检查")
        }
        val user = CurrentUserHolder.get()
        if (req.lock == LockEnum.LOCK.code) {
            var lockList = list.filter { it.submitStatus != SubmitStatusEnum.PENDING.code }
            if (!CollectionUtils.isEmpty(lockList)) {
                throw BusinessException("待提交状态的灵感源才能锁定，请检查！")
            }
            list.forEach {
                it.lockId = user.id
                it.lockName = user.name
                it.submitStatus = SubmitStatusEnum.LOCK.code
            }
        } else if (req.lock == LockEnum.RELEASE.code) {
            var releaseList = list.filter { it.submitStatus != SubmitStatusEnum.LOCK.code || it.lockId != user.id }
            if (!CollectionUtils.isEmpty(releaseList)) {
                throw BusinessException("释放操作只能是状态为锁定或者操作人为当前操作人！")
            }
            list.forEach {
                it.submitStatus = SubmitStatusEnum.PENDING.code
                it.lockId = null
                it.lockName = ""
            }
        }
        inspirationRepository.updateBatchById(list)
    }

    override fun cancel(req: InspirationCancelReq) {
        val list = inspirationRepository.listByIds(req.inspirationIds)
        if (CollectionUtils.isEmpty(list) || req.inspirationIds.size != list.size) {
            throw BusinessException("灵感id不存在，请检查")
        }
        val excludedStatuses = setOf(
            SubmitStatusEnum.PENDING.code, SubmitStatusEnum.LOCK.code, SubmitStatusEnum.WAIT_CONFIRM.code
        )
        val checklist = list.filter { it.submitStatus !in excludedStatuses }
        if (!CollectionUtils.isEmpty(checklist)) {
            throw BusinessException("状态为待提交、待确认、锁定中才能进行淘汰，请检查！")
        }
        list.forEach {
            it.submitStatus = SubmitStatusEnum.CANCEL.code
            it.cancelCode = req.cancelCode
            it.cancelName = req.cancelName
        }
        inspirationRepository.updateBatchById(list)
    }

    /**
     * 仿款提交
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun imitationSubmit(req: InspirationImitationSubmitReq) {
        log.info { "imitationSubmit req=${req.toJson()}" }
        val inspirations = verifyImitationSubmitParam(req)
//        InspirationImitationDTO
        inspirations.forEach {
            it.imitationType = req.imitationType
            val inspirationImitationDTO = InspirationImitationDTO()
            inspirationImitationDTO.imitationType = req.imitationType
            inspirationImitationDTO.spuId = req.spuId
            inspirationImitationDTO.productPictureList = req.productPictureList
            inspirationImitationDTO.skcList = req.skcList?.map { skc ->
                val inspirationImitationSkcDTO = InspirationImitationSkcDTO()
                inspirationImitationSkcDTO.skcId = skc.skcId
                inspirationImitationSkcDTO.skuIdList = skc.skuIdList
                inspirationImitationSkcDTO
            }

            inspirationImitationDTO.skc1688List = req.skc1688List?.map { skc ->
                val inspirationImitation1688SkcDTO = InspirationImitation1688SkcDTO()
                inspirationImitation1688SkcDTO.skcColor = skc.skcColor
                inspirationImitation1688SkcDTO.sizeList = skc.sizeList
                inspirationImitation1688SkcDTO
            }
            inspirationImitationDTO.styleLabel = req.styleLabel
            it.imitationParam = inspirationImitationDTO.toJson()
            it.submitStatus = SubmitStatusEnum.WAIT_CONFIRM.code
            it.planningType = req.styleLabel?.planningType
            it.marketCode = req.styleLabel?.marketCode
            it.marketSeriesCode = req.styleLabel?.marketSeriesCode
            it.marketStyleCode = req.styleLabel?.suggestedStyleCode
        }
        inspirationRepository.updateBatchById(inspirations)
    }

    /**
     * 仿款确认
     */
    override fun imitationConfirm(req: InspirationImitationConfirmReq): Pair<Set<Long>, Set<String>> {
        log.info { "imitationConfirm req=${req.toJson()}" }
        val errors = mutableSetOf<String>()
        val successes = mutableSetOf<Long>()
        val inspirations = verifyImitationConfirmParam(req)

        val imitationTypeEnum = InspirationImitationTypeEnum.of(req.imitationType ?: 0)
        if (imitationTypeEnum == null) {
            throw BusinessException("imitationType is required")
        }

        inspirations.forEach {
            when (imitationTypeEnum) {
                InspirationImitationTypeEnum.INNER -> {
                    try {
                        inspirationImitationComponent.innerSubmit(it, req, successes)
                    } catch (e: Exception) {
                        log.error(e) { "innerSubmit error: ${e.message}" }
                        e.message?.let { it1 -> errors.add(it1) }
                    }
                }

                InspirationImitationTypeEnum.SELECTION -> {
                    try {
                        inspirationImitationComponent.selectionSubmit(it, req, successes)
                    } catch (e: Exception) {
                        log.error(e) { "selectionSubmit error: ${e.message}" }
                        e.message?.let { it1 -> errors.add(it1) }
                    }
                }
            }
        }
        return Pair(successes, errors)

    }


    /**
     * 同步同款标签
     */
    /**
     * 同步同款标签 （这个方法有点长需要优化下）
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun syncSimilarLabel(inspiration: Inspiration) {
        log.info { "syncSimilarLabel inspirationId=${inspiration.inspirationId} countrySiteCode=${inspiration.countrySiteCode}" }

        val query = StyleLibrarySimilarQuery().apply {
            this.busId = inspiration.inspirationId
            this.styleImg = inspiration.inspirationImage
        }
        log.info { "开始查询相似款式 req=$query" }

        PlanningSourceEnum.getByCode(inspiration.planningSourceCode) ?: return

        withSystemUser {
            // 查询相似款式
            val similarStyleLabels = mutableSetOf<Int>()
            var similarList = emptyList<StyleLibraryVo>()

            try {
                val resp = styleLibraryClient.findSimilarList(query)
                log.info { "查询相似款式结果 resp=$resp" }

                if (resp.successful && resp.code == 200) {
                    similarList = resp.data ?: emptyList()
                    if (similarList.isNotEmpty()) {
                        similarList.forEach {
                            when (it.similarDegree) {
                                1 -> similarStyleLabels.add(SimilarStyleLabelEnum.EXISTING_SIMILAR.code)
                                2 -> similarStyleLabels.add(SimilarStyleLabelEnum.EXISTING_SIMILAR_STYLE.code)
                            }
                        }

                        val busIds = similarList
                            .filter { it.sourceType == SOURCE_TYPE_INSPIRATION }
                            .map { it.busId }

                        if (busIds.isNotEmpty()) {
                            val inspirations = inspirationRepository.listByIds(busIds)
                            val similarStyleVO = similarList.map { vo ->
                                val inspirationStyleLibraryVo = InspirationStyleLibraryVo().apply {
                                    categoryId = vo.categoryId
                                    categoryName = vo.categoryName
                                    sourceType = vo.sourceType
                                    busCode = vo.busCode
                                    expandBusCode = vo.expandBusCode
                                    styleImg = vo.styleImg
                                    similarDegree = vo.similarDegree
                                    if (vo.sourceType == SOURCE_TYPE_INSPIRATION && inspirations.isNotEmpty()) {
                                        styleStatus = inspirations.firstOrNull {
                                            it.inspirationCode == vo.busCode
                                        }?.submitStatus
                                    }
                                }
                                inspirationStyleLibraryVo
                            }
                            inspiration.similarStyle = similarStyleVO.toJson()
                        } else {
                            val similarStyleVO = similarList.map { vo ->
                                val inspirationStyleLibraryVo = InspirationStyleLibraryVo().apply {
                                    categoryId = vo.categoryId
                                    categoryName = vo.categoryName
                                    sourceType = vo.sourceType
                                    busCode = vo.busCode
                                    expandBusCode = vo.expandBusCode
                                    styleImg = vo.styleImg
                                    similarDegree = vo.similarDegree
                                }
                                inspirationStyleLibraryVo
                            }
                            inspiration.similarStyle = similarStyleVO.toJson()
                        }
                    } else {
                        similarStyleLabels.add(SimilarStyleLabelEnum.NO_SIMILAR_STYLE_FOUND.code)
                    }
                } else {
                    similarStyleLabels.add(SimilarStyleLabelEnum.NO_SIMILAR_STYLE_FOUND.code)
                }
            } catch (e: Exception) {
                log.error(e) { "查询相似款式发生异常" }
                similarStyleLabels.add(SimilarStyleLabelEnum.NO_SIMILAR_STYLE_FOUND.code)
            }

            // 查询货源 & 计算价格
            val countrySiteCode = inspiration.countrySiteCode
            val productSameStyleReq = ProductSameStyleReq(inspiration.inspirationId, inspiration.inspirationImage ?: "")
            log.info { "查询同款货源 req $productSameStyleReq" }

            var sameStyleData = emptyList<ProductSameStyleVo>()
            try {
                val sameStyleResp = inspirationProductClient.sameStyle(productSameStyleReq)
                log.info { "查询同款货源结果 $sameStyleResp" }

                if (sameStyleResp.successful && sameStyleResp.code == 200) {
                    sameStyleData = sameStyleResp.data ?: emptyList()
                    if (sameStyleData.isNotEmpty()) {
                        sameStyleData.forEach {
                            when (it.similarDegree) {
                                1 -> similarStyleLabels.add(SimilarStyleLabelEnum.PUSHED_SIMILAR_SOURCE.code)
                                2 -> similarStyleLabels.add(SimilarStyleLabelEnum.SIMILAR_SOURCE.code)
                            }
                        }
                    } else {
                        similarStyleLabels.add(SimilarStyleLabelEnum.NO_SOURCE_FOUND.code)
                    }
                } else {
                    similarStyleLabels.add(SimilarStyleLabelEnum.NO_SOURCE_FOUND.code)
                }
            } catch (e: Exception) {
                log.error(e) { "查询同款货源发生异常" }
                similarStyleLabels.add(SimilarStyleLabelEnum.NO_SOURCE_FOUND.code)
            }

            // 汇率查询
            var cnExchangeRate = BigDecimal.ONE
            if (countrySiteCode in listOf("US", "AR")) {
                try {
                    val exchangeRateQueryReq = ExchangeRateQueryReq().apply {
                        currencyType = "CNY"
                    }
                    val exchangeRateResp = currencyExchangeRateExternalClient.queryExchangeRates(exchangeRateQueryReq)
                    if (exchangeRateResp.successful && exchangeRateResp.code == 200) {
                        cnExchangeRate = exchangeRateResp.data
                            ?.firstOrNull { it.countryCode == "US" }
                            ?.exchangeRate ?: BigDecimal.ONE
                    }
                } catch (e: Exception) {
                    log.error(e) { "查询汇率失败，默认使用 1.0" }
                }
            }

            // 价格计算
            if (countrySiteCode in listOf("US", "AR")) {
                val discount = BigDecimal("0.85")
                val allSameStylePrice = sameStyleData
                    .filter {
                        it.price != null && it.sourceType == SOURCE_TYPE_1688 && it.similarDegree == 2
                    }
                    .map { sameStyle ->
                        val supplyPrice = sameStyle.price ?: BigDecimal.ZERO
                        val pricingMultiple = when {
                            countrySiteCode == "US" && supplyPrice < BigDecimal("15.00") -> BigDecimal("5.00")
                            countrySiteCode == "US" -> BigDecimal("4.00")
                            countrySiteCode == "AR" && supplyPrice < BigDecimal("20.00") -> BigDecimal("5.00")
                            countrySiteCode == "AR" -> BigDecimal("4.00")
                            else -> BigDecimal("5.00")
                        }
                        supplyPrice.multiply(pricingMultiple).multiply(cnExchangeRate)
                    }

                if (allSameStylePrice.isNotEmpty()) {
                    val sum = allSameStylePrice.reduce { acc, it -> acc.add(it) }
                    val averagePrice = sum.divide(BigDecimal(allSameStylePrice.size), 2, RoundingMode.HALF_UP)
                    val discountedPrice = averagePrice.multiply(discount).setScale(2, RoundingMode.HALF_UP)
                    inspiration.similarAveragePrice = discountedPrice.toString()

                    inspiration.salePrice?.takeIf { it.isNotBlank() }?.let { salePriceStr ->
                        val finalSalePrice = try {
                            val split = salePriceStr.split("-").last().trim()
                            BigDecimal(split)
                        } catch (e: Exception) {
                            log.warn { "解析灵感源价格失败: $salePriceStr" }
                            null
                        }

                        finalSalePrice?.let {
                            if (discountedPrice > it) {
                                similarStyleLabels.add(SimilarStyleLabelEnum.NO_ADVANTAGEOUS_PRICE_STYLE.code)
                            } else {
                                similarStyleLabels.add(SimilarStyleLabelEnum.ADVANTAGEOUS_PRICE_STYLE.code)
                            }
                        }
                    }
                }
            }

            val updateWrapper = KtUpdateWrapper(Inspiration::class.java);
            updateWrapper.eq(Inspiration::inspirationId, inspiration.inspirationId)
            updateWrapper.set(Inspiration::similarStyleLabel,  similarStyleLabels.toJson())
            updateWrapper.set(Inspiration::similarAveragePrice,  inspiration.similarAveragePrice)
            updateWrapper.set(Inspiration::similarStyle,  inspiration.similarStyle)
            inspirationRepository.update(updateWrapper)
        }
    }

    companion object {
        private const val SOURCE_TYPE_INSPIRATION = "INSPIRATION"
        private const val SOURCE_TYPE_1688 = "1688"
    }


    /**
     * 再次仿款确认
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun imitationReConfirm(req: InspirationImitationReConfirmReq) {
        log.info { "imitationReConfirm req=${req.toJson()}" }
        val imitationTypeEnum = InspirationImitationTypeEnum.of(req.imitationType ?: 0)
        if (imitationTypeEnum == null) {
            throw BusinessException("imitationType is required")
        }
        val inspirationId = req.inspirationId
        val inspiration = inspirationRepository.getById(inspirationId)
        val existed =
            inspiration != null && inspiration.deleted == Bool.NO.code && (inspiration.submitStatus == SubmitStatusEnum.SUBMITTED.code)
        if (!existed) {
            throw BusinessException("灵感源数据不存在")
        }
        if (imitationTypeEnum == InspirationImitationTypeEnum.SELECTION) {
            if (req.spuId == null) {
                throw BusinessException("spuId is null")
            }
            if(req.skcList.isNullOrEmpty() && req.skc1688List.isNullOrEmpty()){
                throw BusinessException("skcList and skc1688List is empty")
            }

            val skcList = req.skcList
            if (!skcList.isNullOrEmpty()) {
                skcList.forEach {
                    if (it.skcId == null) {
                        throw BusinessException("skcId is null")
                    }
                }
            }

            val skc1688List = req.skc1688List
            if(!skc1688List.isNullOrEmpty()){
                req.skc1688List?.forEach {
                    if (it.skcColor == null) {
                        throw BusinessException("skcColor is null")
                    }
                }
            }
        }

        val inspirationImitationConfirmReq = InspirationImitationConfirmReq()
        inspirationImitationConfirmReq.imitationType = req.imitationType
        inspirationImitationConfirmReq.styleLabel = req.styleLabel
        inspirationImitationConfirmReq.spuId = req.spuId
        inspirationImitationConfirmReq.skcList = req.skcList
        inspirationImitationConfirmReq.productPictureList = req.productPictureList
        inspirationImitationConfirmReq.skc1688List = req.skc1688List
        when (req.imitationType) {
            InspirationImitationTypeEnum.INNER.code -> {
                try {
                    inspirationImitationComponent.innerSubmit(
                        inspiration,
                        inspirationImitationConfirmReq,
                        mutableSetOf()
                    )
                } catch (e: Exception) {
                    log.error(e) { "innerSubmit error: ${e.message}" }
                    throw e
                }
            }

            InspirationImitationTypeEnum.SELECTION.code -> {
                try {
                    inspirationImitationComponent.selectionSubmit(
                        inspiration,
                        inspirationImitationConfirmReq,
                        mutableSetOf()
                    )
                } catch (e: Exception) {
                    log.error(e) { "selectionSubmit error: ${e.message}" }
                    throw e
                }
            }
        }
    }

    override fun createSimilarStyle() {
        log.info { "createSimilarStyle come in" }
        val toCreate = inspirationRepository.listToCreateSimilarStyle()
        log.info { "toCreate size =${toCreate.size}" }
        toCreate.filter {
            it.inspirationImage.isNotBlank()
        }.forEach { inspiration ->
            imageHandlerExecutor.execute {
                withSystemUser {
                    // 插入相似度向量
                    val styleLibraryReq = StyleLibraryReq()
                    styleLibraryReq.sourceType = "INSPIRATION"
                    styleLibraryReq.busId = inspiration.inspirationId
                    styleLibraryReq.busCode = inspiration.inspirationCode
                    styleLibraryReq.styleImg = inspiration.inspirationImage
                    styleLibraryReq.tenantId = CurrentUserHolder.get().tenantId
                    styleLibraryReq.categoryName = inspiration.externalCategory
                    styleLibraryReq.creatorId = CurrentUserHolder.get().id
                    styleLibraryReq.creatorName = CurrentUserHolder.get().name
                    log.info { "灵感创建相似度styleLibraryReq=${styleLibraryReq.toJson()}" }
                    val createResp = styleLibraryClient.create(styleLibraryReq)
                    log.info { "灵感创建相似度createResp=${createResp.toJson()}" }
                    if (createResp.successful && createResp.code == 200){
                        inspiration.similarId = createResp.data.toString()
                        inspirationRepository.updateById(inspiration)
                    }
                }
            }
        }


    }


    private fun verifyImitationConfirmParam(req: InspirationImitationConfirmReq): List<Inspiration> {

        val imitationTypeEnum = InspirationImitationTypeEnum.of(req.imitationType ?: 0)
        if (imitationTypeEnum == null) {
            throw BusinessException("imitationType is required")
        }
        if (imitationTypeEnum == InspirationImitationTypeEnum.SELECTION) {
            if (req.spuId == null) {
                throw BusinessException("spuId is null")
            }
            if(req.skcList.isNullOrEmpty() && req.skc1688List.isNullOrEmpty()){
                throw BusinessException("skcList and skc1688List is empty")
            }

            val skcList = req.skcList
            if (!skcList.isNullOrEmpty()) {
                skcList.forEach {
                    if (it.skcId == null) {
                        throw BusinessException("skcId is null")
                    }
                }
            }

            val skc1688List = req.skc1688List
            if(!skc1688List.isNullOrEmpty()){
                req.skc1688List?.forEach {
                    if (it.skcColor == null) {
                        throw BusinessException("skcColor is null")
                    }
                }
            }
        }

        val inspirations = inspirationRepository.listByIds(req.inspirationIds)
        if (inspirations.isEmpty()) {
            throw BusinessException("灵感源数据不存在")
        }

        val count = inspirations.count {
            it.deleted == Bool.NO.code && (it.submitStatus == SubmitStatusEnum.WAIT_CONFIRM.code)
        }
        if (count != inspirations.size || count != req.inspirationIds?.size) {
            throw BusinessException("灵感源数据状态有误")
        }
        return inspirations
    }

    private fun verifyImitationSubmitParam(req: InspirationImitationSubmitReq): List<Inspiration> {
        val imitationTypeEnum = InspirationImitationTypeEnum.of(req.imitationType ?: 0)
        if (imitationTypeEnum == null) {
            throw BusinessException("imitationType is required")
        }
        if (imitationTypeEnum == InspirationImitationTypeEnum.SELECTION) {
            if (req.spuId == null) {
                throw BusinessException("spuId is null")
            }

            if(req.skcList.isNullOrEmpty() && req.skc1688List.isNullOrEmpty()){
                throw BusinessException("skcList and skc1688List is empty")
            }

            val skcList = req.skcList
            if (!skcList.isNullOrEmpty()) {
                skcList.forEach {
                    if (it.skcId == null) {
                        throw BusinessException("skcId is null")
                    }
                }
            }

            val skc1688List = req.skc1688List
            if(!skc1688List.isNullOrEmpty()){
                req.skc1688List?.forEach {
                    if (it.skcColor == null) {
                        throw BusinessException("skcColor is null")
                    }
                }
            }


        }
        val inspirations = inspirationRepository.listByIds(req.inspirationIds)
        if (inspirations.isEmpty()) {
            throw BusinessException("灵感源数据不存在")
        }
        val count = inspirations.count {
            it.deleted == Bool.NO.code && (it.submitStatus == SubmitStatusEnum.PENDING.code || it.submitStatus == SubmitStatusEnum.LOCK.code || it.submitStatus == SubmitStatusEnum.CANCEL.code)
        }
        if (count != inspirations.size || count != req.inspirationIds?.size) {
            throw BusinessException("灵感源数据状态有误")
        }
        inspirations.filter { it.submitStatus == SubmitStatusEnum.LOCK.code }.forEach {
            if (it.lockId != CurrentUserHolder.get().id) {
                throw BusinessException("指定锁定人才能提交")
            }
        }
        return inspirations

    }

    private fun verifySubmitReq(req: InspirationSubmitReq) {
        val inspiration =
            inspirationRepository.getById(req.inspirationId) ?: throw BusinessException("灵感源数据不存在")
        when (SupplyModeEnum.getByCode(req.supplyMethod)) {
            SupplyModeEnum.AIGC -> {
                if (inspiration.identifiedStatus != IdentifiedStatusEnum.VALID.code) {
                    throw RuntimeException("识别不通过, 不能提交AIGC跑图任务")
                }
                if (req.single) {
                    val categoryCode = req.categoryCode
                    val categoryName = req.categoryName
                    if (StringUtils.isBlank(categoryCode)) {
                        throw BusinessException("categoryCode is blank")
                    }
                    if (StringUtils.isBlank(categoryName)) {
                        throw BusinessException("categoryName is blank")
                    }
                }
            }

//            SupplyModeEnum.OBM_REPLICA -> {
//                val expectedCostPrice = req.expectedCostPrice
//                val inside =
//                    expectedCostPrice != null && expectedCostPrice >= BigDecimal("0.00") && expectedCostPrice <= BigDecimal(
//                        "9999.99"
//                    )
//                if (!inside) {
//                    throw BusinessException("expectedCostPrice is null or not in range 0-9999.99")
//                }
//            }

            null -> {
                throw BusinessException("未找到对应的供应方式")
            }

            else -> {}
        }
    }
}
