package tech.tiangong.sdp.service

import jakarta.servlet.http.HttpServletResponse
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.sdp.common.req.AiDesignTaskCreateReq
import tech.tiangong.sdp.common.resp.GetInspirationOrPickingIdResp
import tech.tiangong.sdp.common.resp.InspirationResp
import tech.tiangong.sdp.dao.entity.Inspiration
import tech.tiangong.sdp.req.InspirationImitationReConfirmReq
import tech.tiangong.sdp.req.inspiration.*
import tech.tiangong.sdp.resp.inspiration.InspirationDetailResp
import tech.tiangong.sdp.resp.inspiration.InspirationImportResultVo
import tech.tiangong.sdp.resp.inspiration.InspirationPageResp
import tech.tiangong.sdp.resp.inspiration.InspirationTaskSubmitResp

/**
 * 灵感源
 * <AUTHOR>
 * @date 2024/11/20 09:55
 */
interface InspirationService {

    /**
     * 列表分页
     * @param req 请求对象
     * @return
     */
    fun page(req: InspirationPageReq): PageVo<InspirationPageResp>

    /**
     * 导出
     * @param req 请求对象
     * @return
     */
    fun export(response: HttpServletResponse, req: InspirationPageReq)

    /**
     * Excel导入
     * @param file
     */
    fun importExcel(file: MultipartFile): InspirationImportResultVo

    /**
     * 图片导入
     * @param req
     */
    fun importImage(req: InspirationImportImageReq)

    /**
     * 详情
     * @param inspirationId
     */
    fun detail(inspirationId: Long): InspirationDetailResp


    /**
     * 重新提交-页面回显
     * @param businessId
     */
    fun taskReSubmitDetail(businessId: Long): InspirationTaskSubmitResp

    /**
     * 灵感id/选款id获取相关信息
     * @param inspirationPickingId
     * @return
     */
    fun getByInspirationOrPickingId(inspirationPickingId: Long): GetInspirationOrPickingIdResp

    /**
     * 根据灵感id获取信息
     */
    fun listByIds(idList: List<Long>): List<InspirationResp>;

    /**
     * 提交AI设计任务
     * @param req
     */
    fun submitAiDesignTask(req: AiDesignTaskCreateReq)
    fun remove(inspirationIds: Set<Long>)


    /**
     * 提交任务
     * @param req
     */
    fun submitInspiration(req: InspirationSubmitReq)

    /**
     * 加锁或者释放锁
     */
    fun lockOrRelease(req: InspirationLockReq)

    fun cancel(req: InspirationCancelReq)

    /**
     * 仿款提交
     */
    fun imitationSubmit(req: InspirationImitationSubmitReq)

    /**
     * 仿款确认
     */
    fun imitationConfirm(req: InspirationImitationConfirmReq):Pair<Set<Long>,Set<String>>

    /**
     * 同步同款标签
     */
    fun syncSimilarLabel(inspiration: Inspiration)

    /**
     * 再次仿款确认
     */
    fun imitationReConfirm(req: InspirationImitationReConfirmReq)
    fun createSimilarStyle()
}