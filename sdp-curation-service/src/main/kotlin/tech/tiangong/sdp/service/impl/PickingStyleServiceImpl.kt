package tech.tiangong.sdp.service.impl

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import com.alibaba.fastjson2.parseArray
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.multipart.MultipartFile
import team.aikero.admin.common.vo.DictVo
import team.aikero.blade.auth.withUser
import team.aikero.blade.core.constant.DatePatternConstants.NORM_DATETIME_PATTERN
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.user.holder.DefaultCurrentUserContentSetter
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.req.PlanningSupplyQuantityReq
import tech.tiangong.sdp.common.enums.YesOrNoEnum
import tech.tiangong.sdp.convert.PickStyleConvert
import tech.tiangong.sdp.dao.bo.AttachmentBo
import tech.tiangong.sdp.dao.bo.KeyValueBo
import tech.tiangong.sdp.dao.bo.PickingResultImageInfoBo
import tech.tiangong.sdp.dao.entity.*
import tech.tiangong.sdp.dao.repository.*
import tech.tiangong.sdp.dto.PickingResultExportDTO
import tech.tiangong.sdp.dto.PickingStyleExcelImportDTO
import tech.tiangong.sdp.dto.PickingStyleResultDto
import tech.tiangong.sdp.enums.*
import tech.tiangong.sdp.external.DesignDemandClientExternal
import tech.tiangong.sdp.external.DictClientExternal
import tech.tiangong.sdp.external.InspirationDesignClient
import tech.tiangong.sdp.external.PlanningClientExternal
import tech.tiangong.sdp.req.DesignDemandCreateReq
import tech.tiangong.sdp.req.picking.*
import tech.tiangong.sdp.resp.SmartDevelopOutputGroupTag
import tech.tiangong.sdp.resp.SmartDevelopPromiseTag
import tech.tiangong.sdp.resp.picking.*
import tech.tiangong.sdp.resp.picking.PickingStyleImportResultVo.FailureDetail
import tech.tiangong.sdp.resp.picking.PickingStylePageVo.PickingStylePageResultDetailVo
import tech.tiangong.sdp.resp.picking.PickingStylePageVo.PickingStylePageResultDetailVo.PickingStylePageResultImageVo
import tech.tiangong.sdp.resp.picking.PickingStyleResultDetailVo.PickingDetail
import tech.tiangong.sdp.resp.picking.PickingStyleResultDetailVo.RecommendFabricDetail
import tech.tiangong.sdp.service.PickingStyleService
import tech.tiangong.sdp.service.component.PickingImageComponent
import tech.tiangong.sdp.service.component.UltraHdTaskComponent
import tech.tiangong.sdp.utils.FileExportUtils
import tech.tiangong.sdp.utils.TransactionHelper
import java.io.IOException
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 选款
 * <AUTHOR>
 * @date 2024-11-28 18:21:27
 */
@Slf4j
@Service
class PickingStyleServiceImpl(
    private val aiDesignTaskRepository: AiDesignTaskRepository,
    private val aiDesignTaskFabricRepository: AiDesignTaskFabricRepository,
    private val aiDesignTaskLabelRepository: AiDesignTaskLabelRepository,
    private val pickingAiDesignRepository: PickingAiDesignRepository,
    private val pickingAiDesignStyleRepository: PickingAiDesignStyleRepository,
    private val pickingAiDesignPictureRepository: PickingAiDesignPictureRepository,
    private val pickingAiDesignResultRepository: PickingAiDesignResultRepository,
    private val submitDownstreamLogRepository: SubmitDownstreamLogRepository,
    private val transactionManager: PlatformTransactionManager,
    private val designDemandClientExternal: DesignDemandClientExternal,
    private val planningClientExternal: PlanningClientExternal,
    private val inspirationRepository: InspirationRepository,
    private val aiDesignTaskPictureRepository: AiDesignTaskPictureRepository,
    private val pickingAiHistory2024Repository: PickingAiHistory2024Repository,
    private val dictClientExternal: DictClientExternal,
    private val businessCodeGenerator: BusinessCodeGenerator,
    private val pickingImageComponent: PickingImageComponent,
    private val inspirationDesignClient: InspirationDesignClient,
    private val ultraHdTaskComponent: UltraHdTaskComponent,
) : PickingStyleService {

    companion object {
        /**
         * 日期时间格式化
         */
        private val DATE_TIME_FORMATTER: DateTimeFormatter = DateTimeFormatter.ofPattern(NORM_DATETIME_PATTERN)
    }

    /**
     * 分页
     *
     * @param req 入参
     * @return
     */
    override fun page(req: PickingStylePageReq): PageVo<PickingStylePageVo> {
        val pageData = pickingAiDesignRepository.selectListPage(req)
        if (pageData.records.isEmpty() || pageData.records.isEmpty()) {
            return PageVo(req.pageNum, 0, listOf())
        }

        // 获取款式数据
        val pickingIds = pageData.records.mapNotNull { it.pickingId }.distinct()
        val pickingAiDesignStyleList = pickingAiDesignStyleRepository.selectByPickingIds(pickingIds, req.pickingState)
        val pickingAiDesignStyleMap = pickingAiDesignStyleList.groupBy { it.pickingId }

        // 获取款式图片数据
        val pickingStyleIds = pickingAiDesignStyleList.mapNotNull { it.pickingStyleId }.distinct()
        val pickingAiDesignPictureList = pickingAiDesignPictureRepository.selectByPickingStyleIds(pickingStyleIds)
        val pickingAiDesignPictureMap = pickingAiDesignPictureList.groupBy { it.pickingStyleId }

        // key=字典值code,value=字典值vo
        val waveBatchDictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.PLM_CLOTHING_BAND)?.children?.associateBy({ it.dictCode },
                { it })
        // key=字典值code,value=字典值vo
        val printingDictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.FD_PRINTING)?.children?.associateBy({ it.dictCode }, { it })
        var tags: List<SmartDevelopPromiseTag> = listOf()
        // 查履约情况
        val designTaskIds = pageData.records.mapNotNull { it.designTaskId }
        if (designTaskIds.isNotEmpty()) {
            aiDesignTaskRepository.listByIds(designTaskIds).mapNotNull { it.busId }.let {
                val resp = inspirationDesignClient.listSmartDevelopPromiseTag(it)
                if (resp.successful) {
                    resp.data?.let { promiseTag ->
                        tags = promiseTag
                    }
                }
            }


        }
        // 组装
        return PageVo(req.pageNum, pageData.total.toInt(), pageData.records.map {
            val resp = PickingStylePageVo()
            resp.pickingId = it.pickingId
            resp.dataSourceType = it.dataSource
            resp.countrySiteName = it.countrySiteName
            resp.externalCategory = it.externalCategory
            resp.identifyCategoryName = it.identifyCategoryName
            resp.waveBatchName = waveBatchDictCodeMap?.get(it.waveBatchCode)?.dictName
            resp.taskCode = it.designTaskCode
            resp.creatorId = it.creatorId
            resp.creatorName = it.creatorName
            resp.createdTime = it.createdTime
            resp.inspirationImage = it.inspirationImage
            resp.inspirationSourceType = it.inspirationSourceType
            resp.retailPrice = it.retailPrice
            resp.salePrice = it.salePrice
            resp.pickingStyleDetails = mutableListOf<PickingStylePageResultDetailVo>().apply {
                pickingAiDesignStyleMap[it.pickingId]?.forEach { pickingAiDesignStyle ->
                    val styleVo = PickingStylePageResultDetailVo()
                    styleVo.pickingStyleId = pickingAiDesignStyle.pickingStyleId
                    styleVo.pickingId = pickingAiDesignStyle.pickingId
                    styleVo.pickingStyleName = pickingAiDesignStyle.styleName
                    styleVo.pickingState = pickingAiDesignStyle.pickingState
                    styleVo.sortOrder = pickingAiDesignStyle.sort
                    styleVo.resultDetail = PickingStylePageResultDetailVo.ResultDetailVo().apply {
                        this.suggestedPrice = pickingAiDesignStyle.suggestedPrice?.toString() ?: ""
                        this.suggestedStyle = pickingAiDesignStyle.suggestedStyleName
                        this.suggestedCategoryCode = pickingAiDesignStyle.suggestedCategoryCode
                        this.suggestedWaveBatchCode = pickingAiDesignStyle.suggestedWaveBatchCode
                        this.suggestedShopName = pickingAiDesignStyle.suggestedShopName
                        this.suggestedShopCode = pickingAiDesignStyle.suggestedShopShortCode
                        this.suggestedPrinting =
                            printingDictCodeMap?.get(pickingAiDesignStyle.suggestedPrintingCode)?.dictName
                        this.suggestedCountrySiteCode = pickingAiDesignStyle.suggestedCountrySiteCode
                        this.cargoTrayCode = pickingAiDesignStyle.cargoTrayCode
                        this.remark = pickingAiDesignStyle.remark
                        this.attachments = AttachmentBo.jsonToBoList(pickingAiDesignStyle.attachments)
                        this.updateVersion = pickingAiDesignStyle.updateVersion
                        this.sceneCode = pickingAiDesignStyle.sceneCode
                        this.sceneName = pickingAiDesignStyle.sceneName
                        this.planningType = pickingAiDesignStyle.planningType?.toString()
                        this.marketCode = pickingAiDesignStyle.marketCode
                        this.marketSeriesCode = pickingAiDesignStyle.marketSeriesCode
                        this.suggestedStyleCode = pickingAiDesignStyle.suggestedStyleCode

                    }
                    styleVo.pickingStyleImages = mutableListOf<PickingStylePageResultImageVo>().apply {
                        pickingAiDesignPictureMap[pickingAiDesignStyle.pickingStyleId]?.forEach { pickingAiDesignPicture ->
                            val imageVo = PickingStylePageResultImageVo()
                            imageVo.pickingPictureId = pickingAiDesignPicture.pickingPictureId
                            imageVo.pickingId = pickingAiDesignPicture.pickingId
                            imageVo.pickingStyleId = pickingAiDesignPicture.pickingStyleId
                            imageVo.pictureUrl =
                                getPictureUrl(pickingAiDesignPicture.pictureUrl, pickingAiDesignPicture.repairImgUrl)
                            imageVo.repairImgUrl = pickingAiDesignPicture.repairImgUrl
                            imageVo.groupNum = pickingAiDesignPicture.groupNum
                            imageVo.serialNum = pickingAiDesignPicture.serialNum
                            imageVo.mainImageType = pickingAiDesignPicture.mainImageType
                            imageVo.fixImageType = pickingAiDesignPicture.fixImageType
                            imageVo.eliminateType = pickingAiDesignPicture.eliminateType
                            imageVo.eliminateReasonCodes =
                                if (StringUtils.isNotBlank(pickingAiDesignPicture.eliminateReason)) {
                                    pickingAiDesignPicture.eliminateReason.parseArray<String>()
                                } else {
                                    listOf()
                                }
                            this.add(imageVo)
                        }
                    }
                    this.add(styleVo)
                }
            }
            // 是否选过
            val isPicked =
                resp.pickingStyleDetails?.any { it2 -> PickingStateEnum.isSelected(it2.pickingState) } == true
            resp.state = if (isPicked) 1 else 0
            if (it.designTaskId != null) {
                val aiDesignTask = aiDesignTaskRepository.getById(it.designTaskId)
                if (aiDesignTask != null) {
                    // 图片履约标签
                    val flatMap =
                        tags.filter { tag -> tag.taskId == aiDesignTaskRepository.getById(it.designTaskId).busId }
                            .flatMap { tag ->
                                tag.promiseTagList?.map { promiseTag ->
                                    val smartDevelopOutputGroupTag = SmartDevelopOutputGroupTag()
                                    smartDevelopOutputGroupTag.groupNum = promiseTag.groupNum
                                    smartDevelopOutputGroupTag.pictureId = promiseTag.pictureId
                                    smartDevelopOutputGroupTag.promiseEnabled = promiseTag.promiseEnabled
                                    smartDevelopOutputGroupTag.fabricConsistent = promiseTag.fabricConsistent
                                    smartDevelopOutputGroupTag
                                } ?: emptyList() // 如果 promiseTagList 为空，返回空列表
                            }
                    resp.pickingStyleDetails?.forEachIndexed { index, pickingStylePageResultDetailVo ->
                        flatMap.getOrNull(index).let { groupTag ->
                            pickingStylePageResultDetailVo.styleTag = groupTag
                        }
                    }
                }
            }
            resp
        })
    }

    /**
     * 统计选款状态数量
     *
     * @param req 查询参数（复用 PickingStylePageReq）
     * @return
     */
    override fun countPickingStatus(req: PickingStylePageReq): PickingStyleCountStatusVo {
        val pickingStateCountBoList = pickingAiDesignRepository.countPickingStatus(req)
        // 转为map, key=选款状态, value=数量
        val countMap: MutableMap<Int?, Int?> = mutableMapOf()
        for (pickingStateCountBo in pickingStateCountBoList) {
            countMap[pickingStateCountBo.pickingState] = pickingStateCountBo.count
        }
        return PickingStyleCountStatusVo().apply {
            total = pickingStateCountBoList.mapNotNull { it.count }.sumOf { it }
            toBeSelected = countMap[PickingStateEnum.NOT_AVAILABLE.state] ?: 0
            selected = countMap[PickingStateEnum.AVAILABLE.state] ?: 0
            unselected = countMap[PickingStateEnum.NOT_SELECTED.state] ?: 0
        }
    }

    /**
     * 确认选款
     * @param req
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun confirm(req: PickingConfirmReq) {
        log.info { "确认选款 req: ${req.toJson()}" }
        // verify request params
        val pickingId = req.pickingId
        val selectedStyles = req.result
        verifyConfirmParam(pickingId, selectedStyles)


        // 所有选款数据，先查出来，不要在循环里面单个调用了
        val pickingStyleIds = selectedStyles.map { it.pickingStyleId }
        val pickingAiDesignStyles = pickingAiDesignStyleRepository.listByIds(pickingStyleIds)
        val pickingAiDesignStyleMap = pickingAiDesignStyles.associateBy { it.pickingStyleId }

        // 收集所有需要查询的图片 ID
        val allPictureIds = selectedStyles.flatMap { it -> it.imageInfos?.map { it.pickingPictureId } ?: emptyList() }.distinct().filterNotNull()
        // 批量查询图片信息
        val allPicturesMap = if (allPictureIds.isNotEmpty()) {
            pickingAiDesignPictureRepository.listByIds(allPictureIds).associateBy { it.pickingPictureId }
        } else {
            emptyMap()
        }

        val stylePicturePair = selectedStyles
            .mapNotNull { reqResult ->
                val pickingAiDesignStyle = pickingAiDesignStyleMap[reqResult.pickingStyleId]
                if (pickingAiDesignStyle != null
                    && filterStyle(pickingAiDesignStyle, reqResult)
                    ) {
                    val convertedStyle = PickStyleConvert.convert(
                        pickingAiDesignStyle,
                        reqResult,
                        dictClientExternal
                    )
                    val pickingImageList = reqResult.imageInfos?.mapNotNull { imageInfo ->
                        allPicturesMap[imageInfo.pickingPictureId]?.apply {
                            serialNum = imageInfo.serialNum
                            mainImageType = imageInfo.mainImageType
                            fixImageType = imageInfo.fixImageType
                            eliminateType = imageInfo.eliminateType
                            eliminateReason = imageInfo.eliminateReasonCodes?.toJson()
                        }
                    } ?: mutableListOf()
                    Pair(convertedStyle, pickingImageList)
                } else {
                    null
                }
            }.toMutableList() // 将结果转换为 MutableList
        log.info { "要更新的数据 stylePicturePair: ${stylePicturePair.toJson()}" }
        // 新版本需要生成一个结果
        if (stylePicturePair.isEmpty()) {
            log.info { "无更新数据" }
            return
        }
        // 更新picking_ai_design_style
        val updateStyles = stylePicturePair.map { it.first }
        pickingAiDesignStyleRepository.updateBatchById(updateStyles)
        // 更新 picking_ai_design_picture
        val updatePictures = stylePicturePair.flatMap { it.second }
        if (updatePictures.isNotEmpty()) {
            pickingAiDesignPictureRepository.updateBatchById(updatePictures)
        }
        val pickingAiDesign = pickingAiDesignRepository.getById(req.pickingId)
        // 收集picking_ai_design_result
        val pickingAiDesignResults: MutableList<PickingAiDesignResult> =
            PickStyleConvert.convert(stylePicturePair, pickingAiDesign)
        log.info { "要插入的选款结果pickingAiDesignResults: ${pickingAiDesignResults.toJson()}" }
        pickingAiDesignResultRepository.saveBatch(pickingAiDesignResults)
        // 去生成4K图
        val results = pickingAiDesignResults.filter { PickingStateEnum.AVAILABLE.state == it.pickingState }
        log.info { "要生成4K的选款结果: ${results.toJson()}" }
        if (results.isNotEmpty()) {
            TransactionHelper.afterCommitExecute {
                ultraHdTaskComponent.createTask(results)
            }
        }
    }

    private fun filterStyle(
        pickingAiDesignStyle: PickingAiDesignStyle?,
        pickingStyleConfirmReq: PickingStyleConfirmReq
    ) = !(Objects.equals(pickingStyleConfirmReq.updateVersion, pickingAiDesignStyle?.updateVersion)        // 版本一致, 不需要修改
            || pickingAiDesignStyle?.pickingState == PickingStateEnum.AVAILABLE.state    // 库中数据为已选中, 不需要修改
            || pickingAiDesignStyle?.pickingState == pickingStyleConfirmReq.pickingState) // 库中数据状态和请求数据状态一致, 不需要修改


    private fun verifyConfirmParam(pickingId: Long?, selectedStyles: MutableList<@Valid PickingStyleConfirmReq>) {
        pickingAiDesignRepository.getById(pickingId) ?: throw BusinessException("选款ID不存在")
        selectedStyles.map { it.pickingStyleId }.let {
            val pickingAiDesignStyles = pickingAiDesignStyleRepository.listByIds(it)
            if (pickingAiDesignStyles.isEmpty()) {
                throw BusinessException("选款不存在")
            }
        }
    }

    /**
     * 选款结果 分页
     *
     * @param req 入参
     * @return
     */
    override fun pageResult(req: PickingStyleResultPageReq): PageVo<PickingStyleResultPageVo> {
        // 1. 获取分页数据
        val pageData = pickingAiDesignResultRepository.selectListPage(req)

        if (pageData.records.isEmpty()) {
            return PageVo(req.pageNum, 0, listOf())
        }

        // 2. 批量获取关联数据
        val pickingAiDesignIds = pageData.records.mapNotNull { it.pickingId }.distinct()
        val pickingResultIds = pageData.records.mapNotNull { it.pickingResultId }.distinct()
        val inspirationIds = pageData.records.mapNotNull { it.inspirationId }.distinct()

        // 获取所有需要的关联数据(历史数据,兼容灵感图展示)
        val picking2024Map: Map<Long, PickingAiHistory2024> = pickingAiHistory2024Repository.list(
            KtQueryWrapper(PickingAiHistory2024::class.java).`in`(
                PickingAiHistory2024::pickingResultId,
                pickingResultIds
            )
        )
            .associateBy { it.pickingResultId!! }

        // 获取所有需要的关联数据
        val pickingAiDesignMap: Map<Long, PickingAiDesign> = pickingAiDesignRepository.listByIds(pickingAiDesignIds)
            .associateBy { it.pickingId!! }
        // 考虑可能为空的情况
        val inspirationMap: Map<Long?, Inspiration> = if (inspirationIds.isNullOrEmpty()) {
            emptyMap()
        } else {
            inspirationRepository.listByIds(inspirationIds)
                .associateBy { it.inspirationId }
        }

        // 获取字典数据
        val waveBatchDictCodeMap = dictClientExternal.getTopByDictCode(DictEnum.PLM_CLOTHING_BAND)
            ?.children
            ?.associateBy({ it.dictCode }, { it })

        val trayTypeDictCodeMap = dictClientExternal.getTopByDictCode(DictEnum.TRAY_TYPE)
            ?.children
            ?.associateBy({ it.dictCode }, { it })

        val printingDictCodeMap = dictClientExternal.getTopByDictCode(DictEnum.FD_PRINTING)
            ?.children
            ?.associateBy({ it.dictCode }, { it })


        // 4. 转换数据
        return PageVo(
            req.pageNum,
            pageData.total.toInt(),
            pageData.records.map { record ->
                convertToPickingStyleResultPageVo(
                    record,
                    pickingAiDesignMap,
                    picking2024Map,
                    inspirationMap,
                    waveBatchDictCodeMap,
                    trayTypeDictCodeMap,
                    printingDictCodeMap
                )
            }
        )
    }

    /**
     * 选款结果 详情
     *
     * @param pickingResultId
     * @return
     */
    override fun detailResult(pickingResultId: Long): PickingStyleResultDetailVo {
        val pickingResult =
            pickingAiDesignResultRepository.getById(pickingResultId) ?: throw BusinessException("选款结果不存在")
        val pickingAiDesign = pickingAiDesignRepository.getById(pickingResult.pickingId)
        var designTask: AiDesignTask? = null
        var designLabel: List<KeyValueBo>? = null
        var fabricDetail: List<RecommendFabricDetail>? = null
        if (pickingAiDesign != null && pickingAiDesign.designTaskId != null) {
            designTask = aiDesignTaskRepository.getById(pickingAiDesign.designTaskId!!)
            if (designTask != null) {
                designLabel = aiDesignTaskLabelRepository.getKvByTaskId(designTask.taskId)
                fabricDetail = aiDesignTaskFabricRepository.getRecommendFabricDetailByTaskId(designTask.taskId)
            }
        }
        val marketDictVo = dictClientExternal.getTopByDictCode(DictEnum.MARKET_STYLE)
        // 市场(一级节点)
        val marketMap = marketDictVo?.children?.associateBy({ it.dictCode },
            { it.dictName })
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketDictVo?.children
            ?.flatMap { parent ->
                parent.children.orEmpty().map { child ->
                    child.dictCode to child.dictName
                }
            }
            ?.toMap()
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketDictVo?.children
            ?.flatMap { parent ->
                parent.children.orEmpty().flatMap { child ->
                    child.children.orEmpty().map { style ->
                        style.dictCode to style.dictName
                    }
                }
            }
            ?.toMap()

        return PickingStyleResultDetailVo().apply {
            openStyleState = pickingResult.openStyleState
            styleSpuCode = pickingResult.styleSpuCode ?: ""
            styleSkcCode = pickingResult.styleSkcCode ?: ""
            styleSpuCreateTime = pickingResult.styleSpuCreateTime
            styleEliminateReason = pickingResult.styleEliminateReason ?: ""
            if (pickingAiDesign != null) {
                inspirationDetail = PickingStyleResultDetailVo.InspirationDetail().apply {
                    inspirationImage = pickingAiDesign.inspirationImage
                    externalCategory = pickingAiDesign.externalCategory
                    dataSourceType = pickingAiDesign.dataSource
                    createdTime = pickingAiDesign.createdTime
                    creatorName = pickingAiDesign.creatorName
                    inspirationSourceType = pickingAiDesign.inspirationSourceType
                    countrySiteCode = pickingAiDesign.countrySiteCode
                    retailPrice = pickingAiDesign.retailPrice
                    salePrice = pickingAiDesign.salePrice
                    pickingAiDesign.inspirationId?.let {
                        val inspiration = inspirationRepository.getById(it)
                        if (inspiration != null) {
                            inspirationBrand = inspiration.inspirationBrand
                        }
                    }
                }
            }
            if (designTask != null) {
                designTaskDetail = PickingStyleResultDetailVo.DesignTaskDetail().apply {
                    aiTaskCode = designTask.aiTaskCode
                    category = designTask.categoryName
                    styleType = designTask.styleType
                    generateMode = designTask.generateMode
                    bgEnhanced = designTask.filterBack
                    labels = designLabel
                }
            }
            if (fabricDetail != null) {
                recommendFabricDetails = fabricDetail.filter { it.commodityId != null }
            }
            pickingDetail = pickingResult.let {
                val dto = PickingDetail()
                dto.pickingState = it.pickingState
                dto.selectorId = it.selectorId
                dto.selectorName = it.selectorName
                dto.imagePickingStartTime = it.selectionTime
                dto.suggestedPrice = it.suggestedPrice?.toString()
                dto.suggestedWaveBatchName = it.suggestedWaveBatchCode?.let { it1 ->
                    dictClientExternal.getByDictCode(
                        DictEnum.PLM_CLOTHING_BAND,
                        it1
                    )?.dictName
                }
                if (dto.suggestedWaveBatchName == null) {
                    dto.suggestedWaveBatchName = ""
                }
                dto.planningType = it.planningType?.toString()
                dto.marketCode = it.marketCode
                dto.marketName = marketMap?.get(it.marketCode)
                dto.marketSeriesCode = it.marketSeriesCode
                dto.marketSeriesName = marketSeriesMap?.get(it.marketSeriesCode)
                // TODO 兼容历史数据，后续字典维护好可去掉
                dto.suggestedStyleName = it.suggestedStyleName ?: marketStyleMap?.get(it.suggestedShopCode)

                dto.suggestedShopName = it.suggestedShopName
                dto.suggestedCountrySiteName = it.suggestedCountrySiteName
                dto.suggestedCategoryName = it.suggestedCategoryName
                dto.cargoTrayName =
                    it.cargoTrayCode?.let { it1 -> dictClientExternal.getByDictCode(DictEnum.TRAY_TYPE, it1)?.dictName }
                if (dto.cargoTrayName == null) {
                    dto.cargoTrayName = ""
                }
                dto.suggestedPrintingName = it.suggestedPrintingCode?.let { it1 ->
                    dictClientExternal.getByDictCode(
                        DictEnum.FD_PRINTING,
                        it1
                    )?.dictName
                }
                if (dto.suggestedPrintingName == null) {
                    dto.suggestedPrintingName = ""
                }
                dto.remark = it.remark
                dto.pickingStyleResultDetails = PickingResultImageInfoBo.jsonToBoList(it.resultImageInfo)
                // 特殊处理, 修复图覆盖原图
                dto.pickingStyleResultDetails = dto.pickingStyleResultDetails.map { dtit ->
                    dtit.pictureUrl = getPictureUrl(dtit.pictureUrl, dtit.repairImgUrl)
                    dtit
                }
                dto.sceneCode = it.sceneCode
                dto.sceneName = it.sceneName
                // 商品主题
                pickingResult.pickingStyleId?.let { it1 ->
                    pickingAiDesignStyleRepository.getById(it1)?.let { it2 ->
                        dto.productThemeName = it2.productThemeName
                    }
                }
                dto
            }
        }
    }

    /**
     * 查询选图历史记录
     *
     * @param pickingId 选款id
     * @return
     */
    override fun getPickingStyleHistory(pickingId: Long): List<PickingStyleHistoryVo> {
        val resultList = pickingAiDesignResultRepository.selectListByPickingId(pickingId)
        if (resultList.isEmpty()) {
            return listOf()
        }
        // key=字典值code,value=字典值vo
        val printingDictCodeMap =
            dictClientExternal.getTopByDictCode(DictEnum.FD_PRINTING)?.children?.associateBy({ it.dictCode }, { it })

        return resultList.map {
            val resp = PickingStyleHistoryVo()
            resp.selectorId = it.selectorId
            resp.selectorName = it.selectorName
            resp.selectionTime = it.selectionTime
            resp.pickingStyleResults = mutableListOf<PickingStyleResultDto>().apply {
                val dto = PickingStyleResultDto()
                dto.pickingStyleResultId = it.pickingResultId
                dto.pickingStyleResultDetails = PickingResultImageInfoBo.jsonToBoList(it.resultImageInfo)
                dto.pickingState = it.pickingState
                dto.suggestedPrice = it.suggestedPrice
                dto.serialNum = it.pickingStyleSort
                dto.suggestedStyle = it.suggestedStyleName
                dto.suggestedCategory = it.suggestedCategoryName
                dto.suggestedWave = it.suggestedWaveBatchCode
                dto.suggestedShopName = it.suggestedShopName
                dto.suggestedShopShortCode = it.suggestedShopCode
                dto.suggestedPrinting = printingDictCodeMap?.get(it.suggestedPrintingCode)?.dictName
                dto.suggestedCountrySite = it.suggestedCountrySiteName
                dto.remark = it.remark
                dto.attachments = AttachmentBo.jsonToBoList(it.attachments)
                this.add(dto)
            }
            resp
        }
    }

    /**
     * 导入选款
     *
     * @param file 文件
     * @return
     */
    override fun importPickingStyleList(file: MultipartFile): PickingStyleImportResultVo {
        val user = CurrentUserHolder.get()
        val result = PickingStyleImportResultVo()

        // 字典缓存 key=enum+name
        val dictNameMap = mapOf<String, DictVo?>()

        EasyExcel.read(
            file.inputStream,
            PickingStyleExcelImportDTO::class.java,
            object : AnalysisEventListener<PickingStyleExcelImportDTO>() {
                private val cachedDataList: MutableList<PickingStyleExcelImportDTO> = mutableListOf()

                override fun invoke(data: PickingStyleExcelImportDTO, context: AnalysisContext) {
                    // 在这里处理每一行数据，例如打印出来
                    var isError = false

                    // 抽出公共逻辑
                    fun checkField(check: Boolean, errorMessage: String) {
                        if (check) {
                            val failureDetail = FailureDetail()
                            failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                            failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                            result.failureDetails.add(failureDetail)
                            isError = true
                        }
                    }

                    fun checkFieldDict(dictName: String?, dictEnum: DictEnum, errorMessage: String) {
                        if (!dictName.isNullOrBlank()) {
                            val key = dictEnum.name + dictName
                            val dictVo: DictVo? = if (dictNameMap.containsKey(key)) {
                                dictNameMap[key]
                            } else {
                                dictClientExternal.getFirstByDictName(dictEnum, dictName)
                            }

                            if (dictVo == null) {
                                val failureDetail = FailureDetail()
                                failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                                failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                                result.failureDetails.add(failureDetail)
                                isError = true
                            }
                        }

                    }
                    // 校验
                    checkField(StringUtils.isBlank(data.planningSource), "'*企划来源'不能为空!")
                    checkField(StringUtils.isBlank(data.supplyMethod), "'*选择供给方式'不能为空!")
                    checkField(StringUtils.isBlank(data.waveBatchCode), "'*选择波次'不能为空!")
                    checkField(StringUtils.isBlank(data.inspirationSource), "'*灵感来源'不能为空!")
                    checkField(StringUtils.isBlank(data.inspirationImage), "'*灵感图URL'不能为空!")
                    checkField(StringUtils.isBlank(data.resultImage1), "'*结果图1'不能为空!")
                    // 校验字典
                    checkFieldDict(data.planningSource, DictEnum.PLANNING_SOURCE, "'*企划来源'字典不匹配!")
                    checkFieldDict(data.supplyMethod, DictEnum.SUPPLY_MODE, "'*选择供给方式'字典不匹配!")
                    checkFieldDict(data.waveBatchCode, DictEnum.PLM_CLOTHING_BAND, "'*选择波次'字典不匹配!")
                    if (isError) {
                        result.failCount += 1
                        return
                    }
                    cachedDataList.add(data)
                    result.successCount += 1
                }

                override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                    // 保存数据
                    // 异步执行
                    Thread {
                        saveData()
                    }.start()
                }

                private fun saveData() {
                    if (CollectionUtils.isEmpty(cachedDataList)) {
                        log.warn { "没有数据，不进行存储" }
                        return
                    }

                    val pickingAiDesignList = mutableListOf<PickingAiDesign>()
                    val pickingAiDesignStyleList = mutableListOf<PickingAiDesignStyle>()
                    val pickingAiDesignPictureList = mutableListOf<PickingAiDesignPicture>()
                    cachedDataList.forEach { it ->
                        // 选款 picking_ai_design
                        val pickingAiDesign = PickingAiDesign()
                        pickingAiDesign.pickingId = IdHelper.getId()
                        pickingAiDesign.planningSourceCode = it.planningSource?.let { it1 ->
                            dictClientExternal.getFirstByDictName(
                                DictEnum.PLANNING_SOURCE,
                                it1
                            )?.dictCode
                        }
                        pickingAiDesign.planningSourceName = it.planningSource
                        pickingAiDesign.inspirationSourceType = it.inspirationSource
                        pickingAiDesign.dataSource = PickingDataSourceTypeEnum.IMPORT.content
                        pickingAiDesign.supplyMethodCode = SupplyModeEnum.getByDesc(it.supplyMethod)?.code.toString()
                        pickingAiDesign.supplyMethodName = it.supplyMethod
                        pickingAiDesign.waveBatchCode = it.waveBatchCode
                        pickingAiDesign.externalCategory = it.externalCategory
                        pickingAiDesign.inspirationImage = ""
                        pickingAiDesign.sourceImage = it.inspirationImage
                        pickingAiDesign.tenantId = user.tenantId
                        pickingAiDesignList.add(pickingAiDesign)

                        // 款式
                        val pickingAiDesignStyle = PickingAiDesignStyle()
                        pickingAiDesignStyle.pickingStyleId = IdHelper.getId()
                        pickingAiDesignStyle.pickingId = pickingAiDesign.pickingId
                        pickingAiDesignStyle.styleName = "款式1"
                        pickingAiDesignStyle.pickingState = PickingStateEnum.NOT_AVAILABLE.state
                        pickingAiDesignStyle.sort = 1
                        pickingAiDesignStyle.updateVersion = IdHelper.getId()
                        pickingAiDesignStyle.tenantId = user.tenantId
                        pickingAiDesignStyleList.add(pickingAiDesignStyle)
                        // 图片
                        it.getResultImagesMap().forEach { (index, image) ->
                            val pickingAiDesignPicture = PickingAiDesignPicture()
                            pickingAiDesignPicture.pickingPictureId = IdHelper.getId()
                            pickingAiDesignPicture.pickingId = pickingAiDesignStyle.pickingId
                            pickingAiDesignPicture.pickingStyleId = pickingAiDesignStyle.pickingStyleId
                            pickingAiDesignPicture.pictureUrl = ""
                            pickingAiDesignPicture.sourceUrl = image
                            pickingAiDesignPicture.groupNum = 1
                            pickingAiDesignPicture.serialNum = index
                            pickingAiDesignPicture.mainImageType = if (index == 1) {
                                YesOrNoEnum.YES.code
                            } else {
                                YesOrNoEnum.NO.code
                            }
                            pickingAiDesignPicture.fixImageType = YesOrNoEnum.NO.code
                            pickingAiDesignPicture.eliminateType = YesOrNoEnum.NO.code
                            pickingAiDesignPicture.tenantId = user.tenantId
                            pickingAiDesignPictureList.add(pickingAiDesignPicture)
                        }
                    }
                    // 手动事务
                    TransactionTemplate(transactionManager).executeWithoutResult {
                        pickingAiDesignList.let { pickingAiDesignRepository.saveBatch(it) }
                        pickingAiDesignStyleList.let { pickingAiDesignStyleRepository.saveBatch(it) }
                        pickingAiDesignPictureList.let { pickingAiDesignPictureRepository.saveBatch(it) }
                        TransactionHelper.afterCommitExecute {
                            // 开始处理
                            pickingImageComponent.handler(pickingAiDesignList.mapNotNull { it.pickingId })
                        }
                    }
                    log.info { "导入图片-${cachedDataList.size}条数据" }
                }
            }).sheet().doRead()
        return result
    }

    /**
     * 保存选款数据
     *
     * @param businessId
     * @param dataSourceEnum
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun savePicking(businessId: Long, dataSourceEnum: PickingDataSourceTypeEnum) {
        var user = CurrentUserHolder.get()

        val aiDesignTask =
            aiDesignTaskRepository.getByBusinessId(businessId) ?: throw BusinessException("AI设计任务不存在")
        val aiDesignTaskPictureList = aiDesignTaskPictureRepository.listByTaskId(aiDesignTask.taskId)

        val inspiration =
            inspirationRepository.getById(aiDesignTask.inspirationId) ?: throw BusinessException("灵感源数据不存在")

        // 设置创建用户为 AI任务的创建人(不设置会导致选款的创建人为系统用户)
        if (aiDesignTask.creatorId != null && !aiDesignTask.creatorName.isNullOrBlank()) {
            user = CurrentUser(
                id = aiDesignTask.creatorId!!,
                name = aiDesignTask.creatorName!!,
                code = "",
                tenantId = 0,
                false
            )
            DefaultCurrentUserContentSetter.set(user)
        }

        /*
        组装选款数据
        picking_ai_design
        picking_ai_design_style
        picking_ai_design_picture
         */
        val log = submitDownstreamLogRepository.getByBusinessId(businessId)

        val pickingAiDesign = PickingAiDesign()
        pickingAiDesign.pickingId = IdHelper.getId()
        pickingAiDesign.inspirationId = inspiration.inspirationId
        pickingAiDesign.inspirationImage = inspiration.inspirationImage
        pickingAiDesign.inspirationSourceType = inspiration.inspirationImageSource
        pickingAiDesign.waveBatchCode = log?.waveBatchCode
        pickingAiDesign.planningSourceCode = inspiration.planningSourceCode
        pickingAiDesign.planningSourceName = inspiration.planningSourceName
        pickingAiDesign.supplyMethodCode = SupplyModeEnum.AIGC.code
        pickingAiDesign.supplyMethodName = SupplyModeEnum.AIGC.desc
        pickingAiDesign.designTaskId = aiDesignTask.taskId
        pickingAiDesign.designTaskCode = aiDesignTask.aiTaskCode
        pickingAiDesign.productLink = inspiration.productLink
        pickingAiDesign.dataSource = dataSourceEnum.content
        pickingAiDesign.countrySiteCode = inspiration.countrySiteCode
        pickingAiDesign.countrySiteName = inspiration.countrySiteName
        pickingAiDesign.externalCategory = inspiration.externalCategory
        pickingAiDesign.identifyCategoryCode = inspiration.identifiedCategoryCode
        pickingAiDesign.identifyCategoryName = inspiration.identifiedCategory
        pickingAiDesign.retailPrice = inspiration.retailPrice
        pickingAiDesign.salePrice = inspiration.salePrice
        pickingAiDesign.tenantId = user.tenantId

        pickingAiDesignRepository.save(pickingAiDesign)

        val pickingStyleList = mutableListOf<PickingAiDesignStyle>()
        val pickingPicList = mutableListOf<PickingAiDesignPicture>()
        aiDesignTaskPictureList
            .groupBy { it.groupNum }
            .forEach { (groupNum, images) ->
                val pickingAiDesignStyle = PickingAiDesignStyle()
                pickingAiDesignStyle.pickingStyleId = IdHelper.getId()
                pickingAiDesignStyle.pickingId = pickingAiDesign.pickingId
                pickingAiDesignStyle.pickingState = PickingStateEnum.NOT_AVAILABLE.state
                pickingAiDesignStyle.styleName = "款式$groupNum"
                pickingAiDesignStyle.sort = groupNum
                pickingAiDesignStyle.updateVersion = IdHelper.getId()
                pickingAiDesignStyle.tenantId = user.tenantId
                pickingAiDesignStyle.planningType = aiDesignTask.planningType
                pickingAiDesignStyle.marketCode = aiDesignTask.marketCode
                pickingAiDesignStyle.marketSeriesCode = aiDesignTask.marketSeriesCode
                pickingAiDesignStyle.suggestedStyleCode = aiDesignTask.marketStyleCode

                pickingStyleList.add(pickingAiDesignStyle)

                var index = 0
                images.forEach {
                    val pickingAiDesignPicture = PickingAiDesignPicture()
                    pickingAiDesignPicture.pickingPictureId = IdHelper.getId()
                    pickingAiDesignPicture.pickingId = pickingAiDesignStyle.pickingId
                    pickingAiDesignPicture.pickingStyleId = pickingAiDesignStyle.pickingStyleId
                    pickingAiDesignPicture.pictureUrl = it.pictureUrl
                    pickingAiDesignPicture.repairImgUrl = it.repairImgUrl
                    pickingAiDesignPicture.groupNum = it.groupNum
                    pickingAiDesignPicture.serialNum = it.serialNum
                    pickingAiDesignPicture.mainImageType = if (index == 0) {
                        YesOrNoEnum.YES.code
                    } else YesOrNoEnum.NO.code
                    pickingAiDesignPicture.fixImageType = YesOrNoEnum.NO.code
                    pickingAiDesignPicture.eliminateType = YesOrNoEnum.NO.code
                    pickingAiDesignPicture.tenantId = user.tenantId
                    pickingPicList.add(pickingAiDesignPicture)
                    index += 1
                }
            }
        if (pickingStyleList.isNotEmpty()) {
            pickingAiDesignStyleRepository.saveBatch(pickingStyleList)
        }
        if (pickingPicList.isNotEmpty()) {
            pickingAiDesignPictureRepository.saveBatch(pickingPicList)
        }
    }

    /**
     * 查询统计供给数量
     *
     * @param req
     * @return
     */
    override fun getTotalSupplyQuantity(req: TotalSupplyQuantityReq): TotalSupplyQuantityResp {
        // 获取企划总数
        val totalReq = PlanningSupplyQuantityReq().apply {
            this.categoryCode = req.categoryCode
            this.supplyModeCode = req.supplyModeCode
            this.shopId = req.shopId
        }
        val totalResp = planningClientExternal.getSupplyQuantity(totalReq)
        // 获取总数
        val finishTotalQuantity = pickingAiDesignStyleRepository.getTotalQuantity(req)
        return TotalSupplyQuantityResp().apply {
            this.categoryCode = req.categoryCode
            this.supplyModeCode = req.supplyModeCode
            this.shopId = req.shopId
            this.planningTotalQuantity = totalResp?.totalQuantity ?: 0
            this.finishTotalQuantity = finishTotalQuantity.toInt()
        }
    }

    override fun exportPickingResults(response: HttpServletResponse, req: PickingStyleResultPageReq) {
        req.pageNum = 1
        req.pageSize = 500

        val allData = mutableListOf<PickingResultExportDTO>()

        while (true) {
            req.fixImageType = YesOrNoEnum.YES.code
            val pageData = pageResult(req)

            if (pageData.list.isEmpty()) break

            pageData.list.forEach { record ->
                val problemImageUrls = record.pickingStyleResultDetails
                    .filter { it.fixImageType == YesOrNoEnum.YES.code }
                    .map { it.pictureUrl ?: "" }

                val dto = PickingResultExportDTO(
                    designTaskId = record.designTaskId,
                    designTaskCode = record.designTaskCode,
                    inspirationCode = record.inspirationCode,
                    problemImages = problemImageUrls,
                    attachments = record.attachments.joinToString(",") { it.fileUrl ?: "" },
                    selectorName = record.selectorName,
                    selectionTime = record.selectionTime?.format(DATE_TIME_FORMATTER),
                    openStyleState = PickingOpenStyleStateEnum.getByCode(record.openStyleState)?.desc,
                    styleSpuCode = record.styleCode,
                    remark = record.remark
                )
                allData.add(dto)
            }

            req.pageNum += 1
        }

        val fileName = "修图数据" + businessCodeGenerator.generate(CodeRuleEnum.PICKING_RESULTS_EXPORT) + ".xlsx"

        try {
            exportExcelEntityDynamic(fileName, response, allData)
        } catch (e: IOException) {
            log.error(e) { "导出失败: ${e.message}" }
            throw BusinessException("导出失败: ${e.message}")
        }
    }

    /**
     * 推送选款到sdp-design
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun pushPickingResult() {
        // 查待推送的选款数据
        val results: List<PickingAiDesignResult> = pickingAiDesignResultRepository.listAwait()
        log.info { "pushPickingResult size = ${results.size}" }
        if (results.isEmpty()) {
            return
        }
        results.forEach { pickResult ->
            withUser(
                CurrentUser(
                    pickResult.creatorId ?: 0, pickResult.creatorName ?: "", "",
                    pickResult.tenantId ?: 0, false, null
                )
            ) {
                // 推送到sdp-design
                try {
                    pushSdp(pickResult)
                } catch (e: Exception) {
                    log.error { "push sdp failed pickResultId: ${pickResult.pickingResultId}  message: ${e.message}" }
                }
            }

        }

    }

    @Transactional(rollbackFor = [Exception::class])
    fun pushSdp(pickingAiDesignResult: PickingAiDesignResult) {
        // 推送sdp
        val pickingId = pickingAiDesignResult.pickingId
        val pickingAiDesign: PickingAiDesign = pickingAiDesignRepository.getById(pickingId)
        val designTaskId = pickingAiDesign.designTaskId
        val fabrics: List<RecommendFabricDetail>? = designTaskId?.let {
            aiDesignTaskFabricRepository.getRecommendFabricDetailByTaskId(it)
        }
        val aiDesignTask: AiDesignTask? = designTaskId?.let {
            aiDesignTaskRepository.getById(it)
        }
        val sdkReq = PickStyleConvert.convert(pickingAiDesignResult,pickingAiDesign,dictClientExternal,
            aiDesignTask,fabrics,inspirationRepository,pickingAiDesignPictureRepository,pickingAiDesignStyleRepository)

        val respVo = designDemandClientExternal.create(sdkReq)

        // 新增一个新的记录(选款类型)
        val log = SubmitDownstreamLog()
        log.logId = IdHelper.getId()
        log.inspirationId = pickingAiDesignResult.pickingResultId
        log.logType = 1
        log.businessId = pickingAiDesignResult.pickingResultId
        log.businessCode = ""
        log.waveBatchCode = ""
        log.downstreamTaskId = respVo.designDemandId
        log.taskStatus = TaskStateEnum.SUBMIT.code
        log.generationType = SupplyModeEnum.AIGC.code
        log.request = sdkReq.toJson()
        log.response = respVo.toJson()
        submitDownstreamLogRepository.save(log)
        // 设计需求id
        pickingAiDesignResult.designSubmitStatus = DesignSubmitStatusEnum.PUSHED.code
        pickingAiDesignResult.styleDesignDemandId = respVo.designDemandId
        pickingAiDesignResultRepository.updateById(pickingAiDesignResult)
    }

    /**
     * 扫描生成中的选款任务的图片4K图生成情况
     */

    override fun scanFinishedHdTask() {
        val results: List<PickingAiDesignResult> = pickingAiDesignResultRepository.listGenerating()
        log.info { "scanFinishedHdTask size = ${results.size}" }
        if (results.isEmpty()) {
            return
        }
        results.filter {
            StringUtils.isNotBlank(it.resultImageInfo)
        }.forEach { pickResult ->
            val resultImageInfo = pickResult.resultImageInfo
            val images =
                PickingResultImageInfoBo.jsonToBoList(resultImageInfo).filter { Bool.NO.code == it.eliminateType }
            if (images.isNotEmpty()) {
                val generated: Boolean = scanPickImage(images)
                if (generated) {
                    withUser(
                        CurrentUser(
                            pickResult.creatorId ?: 0, pickResult.creatorName ?: "", "",
                            pickResult.tenantId ?: 0, false, null
                        )
                    ) {
                        // 更新选款结果状态
                        pickResult.designSubmitStatus = DesignSubmitStatusEnum.AWAIT.code
                        pickingAiDesignResultRepository.updateById(pickResult)
                    }

                }
            }
        }


    }

    fun scanPickImage(images: List<PickingResultImageInfoBo>): Boolean {
        val pickingPictureIds = images.map { it.pickingPictureId }
        val pictures = pickingAiDesignPictureRepository.listByIds(pickingPictureIds)
        if (pictures.isEmpty()) {
            return false
        }
        // 生成成功了，或者重试了三次的
        val count = pictures.count { StringUtils.isNotBlank(it.ultraHdPictureUrl) || (it.ultraHdTryTimes ?: 0) >= 3 }
        return pickingPictureIds.size == count
    }

    /**
     * 转换单条记录为VO对象
     */
    private fun convertToPickingStyleResultPageVo(
        record: PickingAiDesignResult,
        pickingAiDesignMap: Map<Long, PickingAiDesign>,
        picking2024Map: Map<Long, PickingAiHistory2024>,
        inspirationMap: Map<Long?, Inspiration>,
        waveBatchDictCodeMap: Map<String, DictVo>?,
        trayTypeDictCodeMap: Map<String, DictVo>?,
        printingDictCodeMap: Map<String, DictVo>?,
    ): PickingStyleResultPageVo {
        return PickingStyleResultPageVo().apply {
            // 基本信息
            pickingResultId = record.pickingResultId
            remark = record.remark
            selectorId = record.selectorId
            selectorName = record.selectorName
            selectionTime = record.selectionTime
            suggestedPrice = record.suggestedPrice?.toString()
            suggestedStyleName = record.suggestedStyleName
            suggestedCategoryName = record.suggestedCategoryName
            suggestedWaveBatchName = waveBatchDictCodeMap?.get(record.suggestedWaveBatchCode)?.dictName
            suggestedShopName = record.suggestedShopName
            suggestedCountrySiteName = record.suggestedCountrySiteName
            suggestedPrintingName = printingDictCodeMap?.get(record.suggestedPrintingCode)?.dictName
            cargoTrayName = trayTypeDictCodeMap?.get(record.cargoTrayCode)?.dictName
            pickingState = record.pickingState
            openStyleState = record.openStyleState
            styleCode = record.styleSpuCode
            styleEliminateReason = record.styleEliminateReason

            // 关联数据处理
            pickingAiDesignMap[record.pickingId]?.let {
                inspirationImage = it.inspirationImage
            }

            // 关联数据处理(兼容历史选款数据)
            if (picking2024Map.containsKey(record.pickingResultId) && StringUtils.isBlank(inspirationImage)) {
                picking2024Map[record.pickingResultId]?.let {
                    inspirationImage = it.originalImageUrl
                }
            }

            // 新增：灵感图编号
            inspirationCode = inspirationMap[record.inspirationId]?.inspirationCode

            // 只获取修图的数据
            pickingStyleResultDetails = PickingResultImageInfoBo.jsonToBoList(record.resultImageInfo)
            // 特殊处理, 修复图覆盖原图
            pickingStyleResultDetails = pickingStyleResultDetails.map { dtit ->
                dtit.pictureUrl = getPictureUrl(dtit.pictureUrl, dtit.repairImgUrl)
                dtit
            }

            designTaskId = record.designTaskId

            designTaskCode = record.designTaskCode

            attachments = AttachmentBo.jsonToBoList(record.attachments)
        }
    }

    private fun exportExcelEntityDynamic(
        fileName: String,
        response: HttpServletResponse,
        dataList: List<PickingResultExportDTO>,
    ) {
        val workbook = WorkbookFactory.create(true)
        val sheet = workbook.createSheet("Sheet1")

        // 定义列宽配置
        val columnWidths = mapOf(
            "任务ID" to 15,
            "跑图任务编号" to 20,
            "灵感图编号" to 20,
            "修图建议" to 50,
            "选款人" to 15,
            "选款日期" to 20,
            "开款状态" to 15,
            "款号" to 20,
            "备注" to 30
        )

        val baseHeaders = listOf("任务ID", "跑图任务编号", "灵感图编号")
        val middleHeaders = listOf("修图建议", "选款人", "选款日期", "开款状态", "款号", "备注")

        // 计算最大URL数量并创建URL表头
        val maxUrls = dataList.maxOf { it.problemImages.size }
        val urlHeaders = List(maxUrls) { i -> "图片URL${i + 1}" }

        // 重新组合headers，将urlHeaders放在最后
        val headers = baseHeaders + middleHeaders + urlHeaders

        // Set headers
        val headerRow = sheet.createRow(0)
        headers.forEachIndexed { index, header ->
            headerRow.createCell(index).setCellValue(header)
        }

        // Create data rows
        dataList.forEachIndexed { rowIndex, dto ->
            val row = sheet.createRow(rowIndex + 1)

            // 填充基础数据
            with(row) {
                createCell(0).setCellValue(dto.designTaskId?.toString() ?: "")
                createCell(1).setCellValue(dto.designTaskCode ?: "")
                createCell(2).setCellValue(dto.inspirationCode ?: "")
            }

            // 填充中间数据
            val middleOffset = baseHeaders.size
            with(row) {
                createCell(middleOffset).setCellValue(dto.attachments ?: "")
                createCell(middleOffset + 1).setCellValue(dto.selectorName ?: "")
                createCell(middleOffset + 2).setCellValue(dto.selectionTime ?: "")
                createCell(middleOffset + 3).setCellValue(dto.openStyleState ?: "")
                createCell(middleOffset + 4).setCellValue(dto.styleSpuCode ?: "")
                createCell(middleOffset + 5).setCellValue(dto.remark ?: "")
            }

            // 填充URL数据（放在最后）
            val urlOffset = baseHeaders.size + middleHeaders.size
            dto.problemImages.forEachIndexed { index, url ->
                row.createCell(urlOffset + index).setCellValue(url)
            }
        }

        // 设置列宽 - 选择以下两种方式之一

        // 方式1：自动适配列宽
        fun autoSizeColumns() {
            headers.indices.forEach { index ->
                sheet.autoSizeColumn(index)
                // 获取自动调整后的宽度，并增加一点余量
                val currentWidth = sheet.getColumnWidth(index)
                sheet.setColumnWidth(index, (currentWidth * 1.2).toInt())
            }
        }

        // 方式2：固定列宽
        fun setFixedColumnWidths() {
            headers.forEachIndexed { index, header ->
                val width = when {
                    // URL列固定宽度
                    header.startsWith("图片URL") -> 50
                    // 其他列使用预定义宽度，如果没有预定义则使用默认值
                    else -> columnWidths[header] ?: 20
                }
                // 256是Excel的单位转换（1个字符约等于256个单位）
                sheet.setColumnWidth(index, 256 * width)
            }
        }

        // 选择使用哪种方式设置列宽
        // autoSizeColumns() // 自动适配列宽
        setFixedColumnWidths() // 固定列宽

        try {
            FileExportUtils.writeOutputStream(response, fileName) { os ->
                workbook.write(os)
            }
        } finally {
            workbook.close()
        }
    }

    /**
     * 优先使用修复图
     *
     * @param pictureUrl
     * @param repairImgUrl
     * @return
     */
    private fun getPictureUrl(pictureUrl: String?, repairImgUrl: String?): String {
        return if (!repairImgUrl.isNullOrBlank()) {
            repairImgUrl
        } else {
            pictureUrl ?: ""
        }
    }
}
