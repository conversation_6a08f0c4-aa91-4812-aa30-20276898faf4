package tech.tiangong.sdp

import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.ComponentScan

@SpringBootApplication
@EnableFeignClients(
    basePackages = [
        "com.zjkj.aigc",
//        "tech.tiangong.sdp.design.client",
        "tech.tiangong.butted",
//        "tech.tiangong.inspiration",
        "tech.tiangong.pop",
//        "tech.tiangong.inspiration",
        "team.aikero.admin.sdk.client",
        "tech.tiangong.sdp.external",
//        "com.arsenal.file.manager.sdk.client",
        "tech.tiangong.bfg.sdk.client",
    ]
)
@ComponentScan(
    basePackages = [
        "tech.tiangong.pop",
        "tech.tiangong.sdp",
        "team.aikero.blade",
//        "com.arsenal.file",
    ]
)
@MapperScan(basePackages = ["tech.tiangong.**.mapper"])
class SdpApplication

fun main(args: Array<String>) {
    runApplication<SdpApplication>(*args)
}
