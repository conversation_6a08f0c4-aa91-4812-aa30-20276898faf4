package tech.tiangong.sdp.enums

/**
 * 仿款类型 10-内部拆版 20-现货选款
 */
enum class InspirationImitationTypeEnum(
    val code: Int,
    val desc: String,
) {
    /**
     * 内部拆版
     */
    INNER(10, "内部拆版"),

    /**
     * 现货选款
     */
    SELECTION(20, "现货选款"),
    ;

    /**
     * code获取枚举
     */
    companion object {
        fun of(code: Int): InspirationImitationTypeEnum? {
            return entries.firstOrNull { it.code == code }
        }
    }
}