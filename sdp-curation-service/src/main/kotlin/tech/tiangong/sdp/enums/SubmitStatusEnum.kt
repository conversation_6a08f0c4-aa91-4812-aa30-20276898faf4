package tech.tiangong.sdp.enums

/**
 * 灵感源数据提交状态枚举
 * <AUTHOR>
 * @date 2024-11-20 11:51:02
 */
enum class SubmitStatusEnum(val code: Int, val desc: String) {

    PENDING(10, "待提交"),
    LOCK(20, "锁定中"),
    WAIT_CONFIRM(30, "待确认"),
    SUBMITTED(40, "已提交"),
    CANCEL(50, "已淘汰");

    companion object {
        fun getByCode(code: Int): SubmitStatusEnum? {
            return entries.find { it.code == code }
        }
    }
}
