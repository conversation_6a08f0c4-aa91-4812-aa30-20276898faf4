package tech.tiangong.sdp.enums

/**
 * 字典枚举-对应字典服务列表
 * <AUTHOR>
 * @date 2024-12-9 23:50:59
 */
enum class DictEnum(val dictCode: String, val desc: String) {
    PLM_CLOTHING_BAND("plm_clothing_band", "款式波段"),
    SKC_CANCEL_REASON("skc_cancel_reason", "SKC取消原因"),
    TRAY_TYPE("tray_type", "货盘类型"),
    RUNNING_DIAGRAM_PROBLEM("running_diagram_problem", "跑图问题反馈"),
    INSPIRATION_CANCEL_REASON("inspiration_cancel_reason", "灵感淘汰原因"),
    PLANNING_SOURCE("planning_source", "企划来源"),
    SUPPLY_MODE("supply_mode", "供给方式（商品类型）"),
    NATIONAL("national", "国家站点"),
    CLOTHING_CATEGORY("clothing_category", "内部品类"),
    FD_PRINTING("fd-printing", "建议印花"),
    JV_STYLE("jv-style", "风格"),
    INSPIRATION_IMAGE_SOURCE("Inspiration_Image_Source", "灵感图来源"),
    INSPIRATION_BRAND("inspiration_brand", "灵感图品牌"),
    MARKET_STYLE("Market_Style", "市场风格库"),
    NATIONAL_MARKET("National_Market", "市场国家库"),

}