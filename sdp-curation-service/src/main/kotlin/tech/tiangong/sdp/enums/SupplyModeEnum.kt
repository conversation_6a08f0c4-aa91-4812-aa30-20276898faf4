package tech.tiangong.sdp.enums

/**
 * 供给方式枚举
 * <AUTHOR>
 * @date 2024-11-19 14:57:26
 */
enum class SupplyModeEnum(val code: String, val desc: String) {
    AIGC("Artificial", "AIGC"),
    OEM("Equipment", "OEM"),
    OBM_REPLICA("imitation", "仿款"),
    OBM_SPOT_GOODS("Manufacturer", "ODM"),
    LOGO_NUM("digital_printing", "数码印花"),
    TRY_ON("try_on", "现货 try on"),
    ;

    companion object {

        fun getByCode(code: String?): SupplyModeEnum? = entries.firstOrNull { it.code == code }

        fun getByDesc(desc: String?): SupplyModeEnum? = entries.firstOrNull { it.desc == desc }
    }
}
