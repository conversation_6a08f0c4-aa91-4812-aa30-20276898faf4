package tech.tiangong.sdp.enums

import kotlin.collections.find
import kotlin.collections.toList
import kotlin.text.isNullOrEmpty

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/23 20:00
 */
enum class PlanningTypeEnum(val code: Int, val desc: String) {

    // 企划内
    INSIDE(1, "企划内"),

    // 企划外
    OUTSIDE(2, "企划外"),

    // 企划强追
    PURSUE(3, "企划强追");

    ;

    /**
     * 根据code获取枚举
     */
    companion object {
        fun getByCode(code: Int?): PlanningTypeEnum? {
            return entries.find { it.code == code }
        }
        /**
         * 通过描述获取code
         *
         */
        fun getCodeByDesc(desc: String?): Int? {
            if (desc.isNullOrEmpty()){
                return null
            }
            return values().find { it.desc == desc }?.code
        }
        /**
         * 获取所有枚举值列表
         */
        val entries: List<PlanningTypeEnum>
            get() = values().toList()
    }
}