package tech.tiangong.sdp.enums

/**
 * 同款标签
 */
enum class SimilarStyleLabelEnum(val code: Int, val content: String) {
    // 已经有同款
    EXISTING_SIMILAR_STYLE(10, "已有同款"),
    // 正在生成或已有相似款
    EXISTING_SIMILAR(20, "已有相似款"),
    // 待处理的同款货源
    SIMILAR_SOURCE(30, "同款货源"),
    // 已推送的相似款货源
    PUSHED_SIMILAR_SOURCE(40, "相似款货源"),
    // 有价格优势的款式
    ADVANTAGEOUS_PRICE_STYLE(50, "优势价格款"),

    // 未找到同款
    NO_SIMILAR_STYLE_FOUND(60, "未找到同款"),
    // 未找到货源
    NO_SOURCE_FOUND(70, "未找到货源"),

    // 非价格优势的款式
    NO_ADVANTAGEOUS_PRICE_STYLE(80, "非优势价格款"),
    ;


    // 根据state获取枚举
    fun getByState(state: Int?): SimilarStyleLabelEnum? {
        for (value in entries) {
            if (value.code == state)
                return value
        }
        return null
    }

    companion object {

    }
}
