package tech.tiangong.sdp.enums

/**
 * 款式类型：0-净色、1-花型
 * <AUTHOR>
 * @date 2024/12/2 17:23
 */
enum class StyleTypeEnum(
    val code: Int,
    val desc: String,
) {
    /**
     * 0-净色
     */
    CLEAN_COLOR(0, "净色"),

    /**
     * 1-花型
     */
    FLOWER_TYPE(1, "花型"),
    ;

    /**
     * code获取枚举
     */
    companion object {
        fun of(code: Int): StyleTypeEnum? {
            return entries.firstOrNull { it.code == code }
        }
    }
}