package tech.tiangong.sdp.dto

import cn.afterturn.easypoi.excel.annotation.Excel
import cn.afterturn.easypoi.excel.annotation.ExcelTarget
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 灵感导出DTO
 * <AUTHOR>
 * @date 2024/11/20 10:35
 */
@ExcelTarget("InspirationExportDTO")
class InspirationExportDTO {
    @Excel(name = "企划来源", orderNum = "0", width = 15.0)
    var planningSource: String? = null
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    @Excel(name = "企划类型", orderNum = "1", width = 15.0)
    var planningTypeName: String? = null

    @Excel(name = "市场", orderNum = "2", width = 15.0)
    var marketName: String? = null
    @Excel(name = "系列", orderNum = "3", width = 15.0)
    var marketSeriesName: String? = null
    @Excel(name = "风格", orderNum = "4", width = 15.0)
    var marketStyleName: String? = null

    @Excel(name = "波次", orderNum = "5", width = 15.0)
    var waveBatchCode: String? = null

    @Excel(name = "灵感图", orderNum = "6", width = 20.0)
    var inspirationImage: String? = null

    @Excel(name = "外部品类", orderNum = "7", width = 55.0)
    var externalCategory: String? = null

    @Excel(name = "灵感图来源", orderNum = "8", width = 16.0)
    var inspirationImageSource: String? = null

    @Excel(name = "来源国家站点", orderNum = "9", width = 16.0)
    var sourceCountrySiteName: String? = null

    @Excel(name = "划线价(US)", orderNum = "10", width = 16.0)
    var retailPrice: String? = null

    @Excel(name = "售价(US)", orderNum = "11", width = 16.0)
    var salePrice: String? = null

    @Excel(name = "建议供给方式", orderNum = "12", width = 16.0)
    var suggestedSupplyMethod: String? = null

    @Excel(name = "灵感创建时间", orderNum = "13", format = "yyyy-MM-dd HH:mm:ss", width = 20.0)
    var inspirationCreatedTime: LocalDateTime? = null

    @Excel(name = "数据来源", orderNum = "14", width = 16.0)
    var dataSource: String? = null

    @Excel(name = "商品链接", orderNum = "15", width = 20.0)
    var productLink: String? = null

    @Excel(name = "识别品类", orderNum = "16", width = 12.0)
    var identifiedCategory: String? = null

    @Excel(name = "识别结果", orderNum = "17", width = 12.0)
    var identifiedStatus: String? = null

    @Excel(name = "识别标签", orderNum = "18", width = 30.0)
    var identifiedLabel: String? = null

    @Excel(name = "款式类型", orderNum = "19", width = 12.0)
    var styleType: String? = null

    @Excel(name = "灵感提交次数", orderNum = "20", width = 16.0, type = 10)
    var submitCount: Int? = null

    @Excel(name = "状态", orderNum = "21", width = 12.0)
    var submitStatus: String? = null
}