package tech.tiangong.sdp.dto

import com.alibaba.excel.annotation.ExcelProperty
import java.math.BigDecimal

/**
 * 灵感导入DTO
 * <AUTHOR>
 * @date 2024/11/20 10:35
 */
class InspirationImportDTO {

    @ExcelProperty(value = ["*供给方式"], index = 0)
    var suggestedSupplyMethod: String? = null

    @ExcelProperty(value = ["*企划来源"], index = 1)
    var planningSource: String? = null

    @ExcelProperty(value = ["*选择波次"], index = 2)
    var waveBatchCode: String? = null
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    @ExcelProperty(value = ["企划类型"], index = 3)
    var planningTypeName: String? = null

    /**
     * 市场
     */
    @ExcelProperty(value = ["市场"], index = 4)
    var marketName: String? = null
    /**
     * 系列
     */
    @ExcelProperty(value = ["系列"], index = 5)
    var marketSeriesName: String? = null
    /**
     * 风格
     */
    @ExcelProperty(value = ["风格"], index = 6)
    var marketStyleName: String? = null


    @ExcelProperty(value = ["来源国家站点"], index = 7)
    var sourceCountrySiteName: String? = null

    @ExcelProperty(value = ["*图片URL"], index = 8)
    var sourceImage: String? = null

    @ExcelProperty(value = ["商品URL"], index = 9)
    var productLink: String? = null

    @ExcelProperty(value = ["外部品类"], index = 10)
    var externalCategory: String? = null

    @ExcelProperty(value = ["灵感图来源"], index = 11)
    var inspirationImageSource: String? = null

    @ExcelProperty(value = ["灵感源品牌"], index = 12)
    var inspirationBrand: String? = null

    @ExcelProperty(value = ["划线价(US)"], index = 13)
    var retailPrice: BigDecimal? = null

    @ExcelProperty(value = ["售价(US)"], index = 14)
    var salePrice: BigDecimal? = null
}