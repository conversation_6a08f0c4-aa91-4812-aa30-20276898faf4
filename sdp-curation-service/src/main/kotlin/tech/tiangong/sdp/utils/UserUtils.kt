package tech.tiangong.sdp.utils

import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.user.exception.UserGetException
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.user.holder.DefaultCurrentUserContentSetter
import tech.tiangong.sdp.constants.AigcConstant

object UserUtils {
    private val currentUserContentSetter = DefaultCurrentUserContentSetter

    // id是Access的hashCode
    private val ACCESS_USER = CurrentUser(
        AigcConstant.ACCESS_USER_ID,
        AigcConstant.ACCESS_USER_NAME,
        "",
        AigcConstant.TENANT_ID,
        false
    )

    @JvmStatic
    fun user(userId: Long, userName: String, tenantId: Long) = CurrentUser(
        userId,
        userName,
        "",
        tenantId,
        false,
    )

    @JvmStatic
    fun user() = try {
        CurrentUserHolder.get()
    } catch (_: UserGetException) {
        accessUer()
    }

    @JvmStatic
    fun accessUer() = ACCESS_USER

    @JvmStatic
    fun <T> mockUserInvoke(action: () -> T): T = mockUserInvoke(user(), action)

    @JvmStatic
    fun <T> mockUserInvoke(user: CurrentUser, action: () -> T): T = try {
        currentUserContentSetter.set(user)
        action()
    } catch (e: Throwable) {
        throw Exception(e.message.toString(), e)
    } finally {
        currentUserContentSetter.clean()
    }

    @JvmStatic
    fun <T> mockUserInvoke(userId: Long, userName: String, tenantId: Long, action: () -> T): T =
        mockUserInvoke(user(userId, userName, tenantId), action)

}
