package tech.tiangong.sdp.utils

import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson


fun <T> Collection<T>?.toNotEmptyJson(): String? {
    return if (this.isEmpty()) {
        null
    } else {
        this!!.toJson()
    }
}


inline fun <reified T> String?.parseObj(): T? {
    return if (this.isBlank()) {
        null
    } else {
        this!!.parseJson()
    }
}

fun <T> String?.parseObj(clazz: Class<T>): T? {
    return if (this.isBlank()) {
        null
    } else {
        this!!.parseJson(clazz)
    }
}


fun <T> String?.parseList(clazz: Class<T>): List<T>? {
    return if (this.isBlank()) {
        null
    } else {
        this!!.parseJsonList(clazz)
    }
}

