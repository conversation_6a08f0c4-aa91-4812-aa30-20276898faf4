//package tech.tiangong.sdp.utils
//
//import com.github.pagehelper.PageInfo
//import team.aikero.blade.core.protocol.PageVo
//import java.util.function.Supplier
//
///**
// * 分页条件转换器
// * PageRespVo助手
// *
// * <AUTHOR>
// * @date 2020/07/24 12:44:35
// * @since 2020-07-24 11:53
// *
// */
//object PageRespHelper {
//    @JvmStatic
//    fun <T> of(pageInfo: PageInfo<T>): PageVo<T> {
//        return PageVo(pageInfo.pageNum, pageInfo.total.toInt(), pageInfo.list)
//    }
//
//
//    @JvmStatic
//    fun <T> of(pageInfo: PageInfo<T>, list: List<T>): PageVo<T> {
//        return PageVo(pageInfo.pageNum, pageInfo.total.toInt(), list)
//    }
//
//    @JvmStatic
//    fun <T, R> ofR(pageInfo: PageInfo<R>, list: List<T>): PageVo<T> {
//        return PageVo(pageInfo.pageNum, pageInfo.total.toInt(), list)
//    }
//
//
//    @JvmStatic
//    fun <T> of(page: Int, total: Long, list: List<T>): PageVo<T> {
//        return PageVo(page, total.toInt(), list)
//    }
//
//
//    @JvmStatic
//    fun <T> of(pageInfo: PageInfo<T>, supplier: Supplier<List<T>>): PageVo<T> {
//        return PageVo(pageInfo.pageNum, pageInfo.total.toInt(), supplier.get())
//    }
//
//    @JvmStatic
//    fun <T> empty(): PageVo<T> {
//        return PageVo(0, 0, emptyList())
//    }
//
//    @JvmStatic
//    fun <T> empty(pageNum: Int): PageVo<T> {
//        return PageVo(pageNum, 0, emptyList())
//    }
//}
