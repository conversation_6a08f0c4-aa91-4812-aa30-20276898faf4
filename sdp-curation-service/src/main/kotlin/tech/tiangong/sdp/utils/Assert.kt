package tech.tiangong.sdp.utils

import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isEmpty
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty

/**
 * 断言Any不为NULL
 */
fun <T> T?.assertNotNull(message: String? = null): T {
    return this ?: throw NullPointerException(message ?: "空对象错误")
}

/**
 * 断言String不为Blank
 */
fun String?.assertNotBlank(message: String? = null): String {
    if (this.isBlank()) {
        throw IllegalArgumentException(message ?: "字符串不能为空")
    }
    return this!!
}

/**
 * 断言String为Blank
 */
fun String?.assertBlank(message: String? = null): String? {
    if (this.isNotBlank()) {
        throw IllegalArgumentException(message ?: "字符串必须为空")
    }
    return this
}


/**
 * 断言List为空
 */
fun <C : Collection<E>, E> C?.assertEmpty(message: String? = null): List<E> {
    if (this.isNotEmpty()) {
        throw IllegalArgumentException(message ?: "集合不能有数据")
    }
    return listOf()
}

/**
 * 断言List不为空
 */
fun <C : Collection<E>, E> C?.assertNotEmpty(message: String? = null): C {
    if (this.isEmpty()) {
        throw IllegalArgumentException(message ?: "集合不能为空")
    }
    return this!!
}


/**
 * 断言集合个数不能超过max
 */
fun <C : Collection<E>, E> C?.assertSizeMax(max: Int, message: String? = null): C? {
    if (this.isNotEmpty() && this!!.size > max) {
        throw IllegalArgumentException(message ?: "集合个数不能不能超过$max")
    }
    return this
}

/**
 * 断言集合个数不能低于max
 */
fun <C : Collection<E>, E> C?.assertSizeMin(min: Int, message: String? = null): C {
    if (this.isEmpty() || this!!.size < min) {
        throw IllegalArgumentException(message ?: "集合个数不能不能低于$min")
    }
    return this
}

