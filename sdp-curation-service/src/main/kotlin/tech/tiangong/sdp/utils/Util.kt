package tech.tiangong.sdp.utils

import team.aikero.blade.core.toolkit.isEmpty
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

fun ofInstant(timestamp: Long?): LocalDateTime? {
    if (timestamp == null || timestamp <= 0L) {
        return null
    }
    return if (timestamp.toString().length < 13) {
        LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault())
    } else {
        LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
    }
}

fun LocalDateTime.toEpochSeconds(): Long {
    val zonedDateTime = this.atZone(ZoneId.systemDefault())
    val instant = zonedDateTime.toInstant()
    return instant.epochSecond
}

fun LocalDateTime.toEpochMilli(): Long {
    val zonedDateTime = this.atZone(ZoneId.systemDefault())
    val instant = zonedDateTime.toInstant()
    return instant.toEpochMilli()
}

fun <T> Iterable<T>.joinToStr(separator: CharSequence = ","): String {
    return this.joinToString(separator)
}

fun String.splitAndFirst(separator: String = ","): String {
    return this.split(separator).first()
}


fun String?.takeStr(limit: Int? = null): String? {
    if (limit == null || limit <= 0) {
        return this
    }
    if (this.isNullOrBlank()) {
        return this
    }
    return this.take(limit)
}


fun <R> tryExec(fn: () -> R): R? {
    return try {
        fn()
    } catch (e: Throwable) {
        println("tryExec异常：${e.stackTraceToString()}")
        null
    }
}


fun <C : Collection<E>, E, R> C?.emptyExec(fn: () -> R): R? {
    return if (this.isEmpty()) {
        fn()
    } else {
        null
    }
}

fun <C : Collection<E>, E, R> C?.notEmptyExec(fn: (c: C) -> R): R? {
    return if (this.isEmpty()) {
        null
    } else {
        fn(this!!)
    }
}

fun uuid(prefix: Any? = null): String {
    return if (prefix == null) {
        UUID.randomUUID().toString().replace("-", "")
    } else {
        prefix.toString() + UUID.randomUUID().toString().replace("-", "")
    }
}
