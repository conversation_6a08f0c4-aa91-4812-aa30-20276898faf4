package tech.tiangong.sdp.req

import jakarta.validation.constraints.NotNull
import java.io.Serial
import java.io.Serializable

/**
 * 商品加入选款Req
 *
 * <AUTHOR>
 */
data class ProductSameStyleReq(
    /**
     * 业务ID（唯一主键）用来排除本身
     */
    @field:NotNull(message = "业务ID不能为空")
    var busId: Long? = null,
    /**
     * 图片url
     */
    var imageUrl: String ,


) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
