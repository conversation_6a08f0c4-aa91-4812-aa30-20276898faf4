package tech.tiangong.sdp.req.inspiration

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import tech.tiangong.sdp.common.req.StyleLabel
import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal

/**
 * 商品加入选款Req
 *
 * <AUTHOR>
 */
data class ProductSelectionStyleReq(
    /**
     * 供给方式名称
     */
    var supplyModeName: String? = null,

    /**
     * 供给方式编码
     */
    var supplyModeCode: String? = null,
    /**
     * 商品ID
     */
    @field:NotNull(message = "spuId不能为空")
    var spuId: Long? = null,

    /**
     * SKC列表
     */
    @field:NotEmpty(message = "SKC不能为空")
    var skcList: List<ProductSelectionSkcReq>? = null,

    /**
     * 选款状态：0-待选款；10-选款中；11-待报价；12-待确认；20-已中止；25-待建款；30-已完成；50-已淘汰；60-失败；
     */
    var styleSelectionStatus: Int? = null,

    /**
     * 灵感源id
     */
    var inspirationId: Long? = null,


    /**
     * 店铺名称
     */
    var storeName: String? = null,

    /**
     * 波段名称
     */
    var wavebandName: String? = null,

    /**
     * 品类编码
     */
    var categoryCode: String? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,


    /**
     * 期望价格
     */
    var expectedPrice: BigDecimal? = null,


    /**
     * 建议风格
     */
    var modoName: String? = null,


    /**
     * 货盘名称
     */
    var palletTypeName: String? = null,

    /**
     * 款式标签
     */
    var styleLabel: StyleLabel? = null,



    /**
     * 商品图集合
     */
    var productPictureList: List<String>? = null

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }


    data class ProductSelectionSkcReq(
        /**
         * SKC-ID
         */
        @field:NotNull(message = "skcId不能为空")
        var skcId: Long? = null,

        /**
         * SKU-ID列表
         */
        var skuIdList: List<Long>? = null,

        ) : Serializable {
        companion object {
            @Serial
            private const val serialVersionUID: Long = 1L
        }
    }
}
