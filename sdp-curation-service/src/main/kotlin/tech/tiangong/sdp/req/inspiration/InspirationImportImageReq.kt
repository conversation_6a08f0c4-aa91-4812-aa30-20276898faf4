package tech.tiangong.sdp.req.inspiration

import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size


data class InspirationImportImageReq(
    /**
     * 供给方式
     */
    var supplyMethodCode: String? = null,
    /**
     * 波次
     */
    var waveBatchCode: String? = null,
    /**
     * 企划来源
     */
    var planningSourceCode: String? = null,
    /**
     * 灵感图来源 v3.10.1
     */
    var inspirationImageSourceCode: String? = null,
    /**
     * 国家站点code
     */
    var countrySiteCode: String? = null,
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    var planningType: Int? = null,

    /**
     * 市场编码
     */
    var marketCode: String? = null,

    /**
     * 市场风格编码
     */
    var marketStyleCode: String? = null,

    /**
     * 市场系列编码
     */
    var marketSeriesCode: String? = null,
    /**
     * 灵感图(多个url)
     */
    @field:NotNull(message = "灵感图不能为空")
    @field:Size(min = 1, message = "灵感图不能为空")
    var inspirationImages: List<String>? = null,

    /**
     * 灵感源品牌 v3.10.1
     */
    var inspirationBrandCode: String? = null

)

