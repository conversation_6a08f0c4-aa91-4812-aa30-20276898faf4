package tech.tiangong.sdp.req.inspiration

import jakarta.validation.constraints.NotEmpty
import tech.tiangong.sdp.common.req.StyleLabel

/**
 * 仿款确认
 */
class InspirationImitationConfirmReq {

    /**
     * 灵感ID
     */
    @field:NotEmpty(message = "inspirationIds is required")
    var inspirationIds: Set<Long>? = null

    /**
     * 商品ID
     */
//    @field:NotNull(message = "spuId不能为空")
    var spuId: Long? = null

    /**
     * SKC列表
     */
//    @field:NotEmpty(message = "SKC不能为空")
    var skcList: List<InspirationImitationSkcDTO>? = null


    /**
     * SKC1688列表
     */
    var skc1688List: List<InspirationImitation1688SkcDTO>? = null

    /**
     * 款式标签
     */
    var styleLabel: StyleLabel? = null

    /**
     * 仿款类型 10-内部拆版 20-现货选款
     */
    var imitationType: Int? = null



    /**
     * 商品图集合
     */
    var productPictureList: List<String>? = null
}







