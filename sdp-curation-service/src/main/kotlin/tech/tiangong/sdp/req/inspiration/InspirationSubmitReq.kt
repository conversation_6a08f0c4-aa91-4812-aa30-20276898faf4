package tech.tiangong.sdp.req.inspiration

import tech.tiangong.sdp.common.req.AiDesignTaskCreateExt
import tech.tiangong.sdp.common.req.StyleLabel
import tech.tiangong.sdp.dao.bo.AiDesignModelBo
import tech.tiangong.sdp.dao.bo.AiDesignSceneBo
import java.math.BigDecimal

class InspirationSubmitReq(
    var inspirationId: Long,
    /**
     * 波次
     */
    var waveBatchCode: String,

    /**
     * 供给方式
     * @see tech.tiangong.sdp.enums.SupplyModeEnum
     */
    var supplyMethod: String,
) {



    /**
     * 生成模式,1:多姿势,0:单姿势
     */
    var generateMode: Int? = null

    /**
     * 背景增强(1:开启, 0:关闭)
     */
    var filterBack: Int? = null

    /**
     * 脸部修复(1:开启, 0:关闭)
     */
    var faceRepair: Int? = null

    /**
     * 履约增强：0-否；1-是 v3.10.1
     */
    var promiseEnhanced: Int? = null

    /**
     * 场景
     */
    var sceneInfo: AiDesignSceneBo? = null

    /**
     * 模特
     */
    var modelInfo: AiDesignModelBo? = null

    /**
     * 模特素材
     */
    var modelMaterialInfo: ModelMaterialInfoReq? = null

    /**
     * 生成数量
     */
    var generateNum: Int? = null

    /**
     * @since v3.9
     * 期望成本价
     */
//    @NotNull(message = "expectedCostPrice is null")
//    @Range(min = 0, message = "expectedCostPrice must be between 0 and 10000", max = 10000)
    var expectedCostPrice: BigDecimal? = null

    /**
     * 品类编码（aigc才可能传）v3.11
     */
//    @NotBlank(message = "categoryCode is blank")
    var categoryCode: String? = null

    /**
     * 品类名称 (aigc才可能传) v3.11
     */
//    @NotBlank(message = "categoryName is blank")
    var categoryName: String? = null

    /**
     * 是否同步修改灵感识别品类1-是 0-否  v3.11
     */
    var syncCategory: Int? = null

    /**
     * 模型编码（字典配置编码）
     */
    var modeCode: String? = null

    /**
     * 模型名称（字典配置名称）
     */
    var modeName: String? = null

    /**
     * 参考图权重，值越大，参考度越低，建议1~8的浮点数（默认0）v3.11
     */
    var refWeight: BigDecimal? = null

    /**
     * 重试ai设计生图参数
     */
    var createExtParam: AiDesignTaskCreateExt? = null

    /**
     * 是否是单独提交
     */
    var single: Boolean = false

//    /**
//     * 款式标签 v3.13
//     */
//    var styleLabel: InspirationImitationDTO? = null
}


