package tech.tiangong.sdp.req

import jakarta.validation.constraints.NotNull
import tech.tiangong.sdp.common.req.StyleLabel
import tech.tiangong.sdp.req.inspiration.InspirationImitation1688SkcDTO
import tech.tiangong.sdp.req.inspiration.InspirationImitationSkcDTO

/**
 * 仿款确认
 */
class InspirationImitationReConfirmReq {
    /**
     * 仿款类型 10-内部拆版 20-现货选款
     */
    @field:NotNull(message = "imitationType is required")
    var imitationType: Int? = null
    /**
     * 灵感ID
     */
    @field:NotNull(message = "inspirationId is required")
    var inspirationId: Long? = null

    /**
     * 商品ID
     */
//    @field:NotNull(message = "spuId不能为空")
    var spuId: Long? = null

    /**
     * SKC列表
     */
//    @field:NotEmpty(message = "SKC不能为空")
    var skcList: List<InspirationImitationSkcDTO>? = null


    /**
     * SKC1688列表
     */
    var skc1688List: List<InspirationImitation1688SkcDTO>? = null

    /**
     * 款式标签
     */
    var styleLabel: StyleLabel? = null



    /**
     * 商品图集合
     */
    var productPictureList: List<String>? = null


}







