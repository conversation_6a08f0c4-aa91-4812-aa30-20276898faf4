package tech.tiangong.sdp.req.inspiration

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Null

class InspirationLockReq(

    /**
     * 灵感id
     */
    @field:NotEmpty(message = "inspirationIds is empty")
    var inspirationIds: List<Long>,

    ) {

    /**
     * 是否锁定：0-否，1-锁定
     */
    @field:NotNull(message = "lock is null")
    var lock: Int? = null

}


