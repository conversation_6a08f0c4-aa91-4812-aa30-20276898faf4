package tech.tiangong.sdp.req.inspiration

import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import tech.tiangong.sdp.common.req.StyleLabel
import tech.tiangong.sdp.req.inspiration.ProductSelectionStyleReq.ProductSelectionSkcReq
import java.io.Serial
import java.io.Serializable

/**
 * 仿款提交
 */
class InspirationImitationSubmitReq {
    /**
     * 仿款类型 10-内部拆版 20-现货选款
     */
    @field:NotNull(message = "imitationType is required")
    var imitationType: Int? = null
    /**
     * 灵感ID
     */
    @field:NotEmpty(message = "inspirationIds is required")
    var inspirationIds: Set<Long>? = null

    /**
     * 商品ID
     */
//    @field:NotNull(message = "spuId不能为空")
    var spuId: Long? = null

    /**
     * SKC列表
     */
//    @field:NotEmpty(message = "SKC不能为空")
    var skcList: List<ImitationProductSelectionSkcReq>? = null

    /**
     * skc1688列表  用来接收1688图搜的商品
     */
    var skc1688List: List<ImitationProduct1688SelectionSkcReq>? = null

    /**
     * 款式标签
     */
    var styleLabel: StyleLabel? = null


    /**
     * 商品图集合
     */
    var productPictureList: List<String>? = null


}

data class ImitationProductSelectionSkcReq(
    /**
     * SKC-ID
     */
//    @field:NotNull(message = "skcId不能为空")
    var skcId: Long? = null,

    /**
     * SKU-ID列表
     */
    var skuIdList: List<Long>? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}



data class ImitationProduct1688SelectionSkcReq(
    /**
     * SKC-ID
     */
//    @field:NotNull(message = "skcId不能为空")
    var skcColor: String? = null,

    /**
     * SKU列表
     */
    var sizeList: List<String>? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}

