package tech.tiangong.sdp.resp

import java.io.Serial
import java.io.Serializable

/**
* 选款新增返回
*
* <AUTHOR>
* @date       ：2025/2/26 17:21
* @version    :1.0
*/
class StyleSelectionAddResp : Serializable {

    /**
     * 选款SPU-ID
     */
    var styleSelectionSpuId: Long? = null

    /**
     * 选款SPU编码
     */
    var styleSelectionSpuCode: String? = null


    companion object {
        @Serial
        private const val serialVersionUID: Long = -8243673487583749283L
    }
}