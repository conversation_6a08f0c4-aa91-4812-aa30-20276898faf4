package tech.tiangong.sdp.resp.inspiration

import tech.tiangong.sdp.common.resp.InspirationStyleLibraryVo
import tech.tiangong.sdp.req.inspiration.InspirationImitationDTO
import tech.tiangong.sdp.resp.ProductSameStyleVo
import java.time.LocalDateTime


data class InspirationDetailResp(
    /**
     * 同款均价 v3.13
     */
    var similarAveragePrice: String? = null,
    /**
     * 仿款类型 10-内部拆版 20-现货选款
     */
    var imitationType: Int? = null,
    /**
     * 灵感id
     */
    var inspirationId: Long? = null,
    /**
     * 企划来源
     */
    var planningSourceCode: String? = null,
    /**
     * 波次
     */
    var waveBatchCode: String? = null,
    /**
     * 波次
     */
    var waveBatchName: String? = null,
    /**
     * 灵感图
     */
    var inspirationImage: String? = null,
    /**
     * 外部品类
     */
    var externalCategory: String? = null,
    /**
     * 灵感图来源
     */
    var inspirationImageSource: String? = null,

    /**
     * 灵感图品牌 v3.10.1
     */
    var inspirationBrand: String? = null,
    /**
     * 来源国家站点
     */
    var sourceCountrySiteName: String? = null,
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    var planningType: String? = null,

    /**
     * 市场编码
     */
    var marketCode: String? = null,
    /**
     * 市场名称
     */
    var marketName: String? = null,

    /**
     * 市场风格编码
     */
    var marketStyleCode: String? = null,

    /**
     * 市场风格名称
     */
    var marketStyleName: String? = null,

    /**
     * 市场系列编码
     */
    var marketSeriesCode: String? = null,
    /**
     * 市场系列名称
     */
    var marketSeriesName: String? = null,
    /**
     * 划线价(US)
     */
    var retailPrice: String? = null,
    /**
     * 销售价(US)
     */
    var salePrice: String? = null,
    /**
     * 建议供给方式
     */
    var suggestedSupplyModeCode: String? = null,
    /**
     * 灵感创建时间
     */
    var inspirationCreatedTime: LocalDateTime? = null,
    /**
     * 数据来源
     */
    var dataSource: String? = null,
    /**
     * 识别品类
     */
    var identifiedCategory: String? = null,
    /**
     * 识别结果: 1通过, 0无效
     */
    var identifiedStatus: Int? = null,
    /**
     * 识别标签
     */
    var identifiedLabel: String? = null,
    /**
     * 款式类型：0-净色、1-花型
     */
    var styleType: String? = null,
    /**
     * 灵感提交次数
     */
    var submitCount: Int? = null,
    /**
     * 状态
     */
    var submitStatus: Int? = null,
    /**
     * 创建人名称
     */
    var creatorName: String? = null,
    /**
     * 创建时间
     */
    var createdTime: LocalDateTime? = null,
    /**
     * 商品链接URL
     */
    var productLinkUrl: String? = null,
    /**
     * 任务信息
     */
    var taskInfo: MutableList<TaskInfoItem>? = null,

    /**
     * 仿款提交数据 v3.13
     */
    var imitationReq: InspirationImitationDTO? = null,

    /**
     * 相似款式 v3.13
     */
    var similarStyles: List<InspirationStyleLibraryVo>? = null,

    /**
     * 更新记录 v3.13
     */
    var updateLogs: List<UpdateLog>? = null,
    /**
     * 同款标签 v3.13
     */
    var similarStyleLabels: Set<Int>? = null,
    /**
     * 同款货源 v3.13
     */
    var productSameStyles: List<ProductSameStyleVo>? = null,

    /**
     * 淘汰原因编码
     */
    var cancelCode: String? = null,

    /**
     * 淘汰原因
     */
    var cancelName: String? = null
)


data class TaskInfoItem(
    /**
     * 日志id
     */
    var logId: Long? = null,
    /**
     * 业务id
     */
    var businessId: Long? = null,
    /**
     * 业务code
     */
    var businessCode: String? = null,
    /**
     * 波次
     */
    var waveBatchName: String? = null,
    /**
     * 跑图类型
     */
    var generationType: String? = null,
    /**
     * 提交人
     */
    var submitterName: String? = null,
    /**
     * 提交时间
     */
    var submitTime: LocalDateTime? = null,
    /**
     * 任务状态
     */
    var taskStatus: Int? = null,
    /**
     * 跑图任务编号
     */
    var aiTaskCode: String? = null,
)


data class UpdateLog(
    /**
     * 更新时间
     */
    var updateTime: LocalDateTime? = null,
    /**
     * 任务id
     */
    var taskId: String? = null,
    /**
     * 品池策略id
     */
    var poolId: String? = null,
)

