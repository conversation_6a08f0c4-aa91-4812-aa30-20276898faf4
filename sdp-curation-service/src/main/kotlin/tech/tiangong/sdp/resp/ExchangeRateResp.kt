package tech.tiangong.sdp.resp

import tech.tiangong.pop.common.enums.YesOrNoEnum
import java.math.BigDecimal

/**
 * 汇率列表响应对象
 */
data class ExchangeRateResp(
    /**
     * 汇率配置ID
     */
    var currencyExchangeRateId: Long? = null,

    /**
     * 币种类型代码
     */
    var currencyType: String = "",

    /**
     * 币种类型名称
     */
    var currencyTypeName: String = "",

    /**
     * 国家代码
     */
    var countryCode: String = "",

    /**
     * 国家名称
     */
    var countryName: String = "",

    /**
     * 汇率值
     */
    var exchangeRate: BigDecimal = BigDecimal.ZERO,

    /**
     * 状态：0-禁用 1-启用
     */
    var status: Int = YesOrNoEnum.YES.code
)
