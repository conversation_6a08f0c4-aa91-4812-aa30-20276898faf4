package tech.tiangong.sdp.resp

import java.io.Serial
import java.io.Serializable
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * @description:
 * @author: chazz
 * @since: 2025年05月05日15:46:01
 * @version: 1.0
 **/
data class ProductSameStyleVo(
    /**
     * 来源类型：导入-import；1688-1688
     */
    var sourceType: String? = null,
    /**
     * 货盘类型名称
     */
    var palletTypeName: String? = null,
    /**
     * 商品ID
     */
    var spuId: Long? = null,

    /**
     * 商品名
     */
    var spuName: String? = null,

    /**
     * 商品图片
     */
    var spuImg: String? = null,

    /**
     * 供应商名称
     */
    var supplierName: String? = null,

    /**
     * 供应商款号
     */
    var supplierStyleCode: String? = null,

    /**
     * 品类名称
     */
    var categoryName: String? = null,

    /**
     * 分销信息-是否一件代发包邮
     */
    var onePieceFreePostage: Int? = null,

    /**
     * 分销信息-分销起批量
     */
    var startQuantity: Int? = null,
    /**
     * 销量
     */
    var soldOut: Int? = null,
    /**
     * 价格
     */
    var price: BigDecimal? = null,

    /**
     * 相似程度：1-相似款；2-同款
     */
    var similarDegree: Int? = null,


    /**
     * 商品链接
     */
    var spuLink: String? = null,

    /**
     * 创建人ID
     */
    var creatorId: Long? = null,

    /**
     * 创建人姓名
     */
    var creatorName: String? = null,

    /**
     * 创建时间
     */
    var createdTime: LocalDateTime? = null,

    /**
     * 选款状态：0-未选款；1-已选款
     */
    var selectionStyleStatus: Int? = null,

    /**
     * 选款时间
     */
    var selectionStyleTime: LocalDateTime? = null,

    /**
     * 建款状态：0-未建款；1-已建款
     */
    var buildStyleStatus: Int? = null,

    /**
     * 建款时间
     */
    var buildStyleTime: LocalDateTime? = null,

    ) : Serializable {
    companion object {
        @Serial
        private const val serialVersionUID: Long = 1L
    }
}
