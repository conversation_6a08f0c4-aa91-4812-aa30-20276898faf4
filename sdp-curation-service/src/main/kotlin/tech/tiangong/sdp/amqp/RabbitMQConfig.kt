package tech.tiangong.sdp.amqp

import org.springframework.amqp.core.*
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class RabbitMQConfig {


    companion object {
        const val EXCHANGE_NAME = "delayed_exchange"
        const val QUEUE_NAME = "delayed_queue"
        const val ROUTING_KEY = "delayed_routing_key"
    }

    @Bean
    fun delayedExchange(): CustomExchange {
        val args = mapOf("x-delayed-type" to "direct")
        return CustomExchange(EXCHANGE_NAME, "x-delayed-message", true, false, args)
    }

    @Bean
    fun queue(): Queue {
        return Queue(QUEUE_NAME, true)
    }

    @Bean
    fun binding(queue: Queue, delayedExchange: CustomExchange): Binding {
        return BindingBuilder.bind(queue).to(delayedExchange).with(ROUTING_KEY).noargs()
    }


}