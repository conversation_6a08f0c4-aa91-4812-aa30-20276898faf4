package tech.tiangong.sdp.amqp


import com.rabbitmq.client.Channel
import org.springframework.amqp.core.ExchangeTypes
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.constants.AigcConstant
import tech.tiangong.sdp.constants.MqConstant
import tech.tiangong.sdp.dao.entity.Inspiration
import tech.tiangong.sdp.dao.entity.InspirationAidcSourceHistory
import tech.tiangong.sdp.service.InspirationAidcService
import tech.tiangong.sdp.service.InspirationService
import tech.tiangong.sdp.utils.UserUtils
import java.nio.charset.Charset


@Slf4j
@Component
//@Profile("prod-ola")
class RabbitConsumer(
    private val inspirationService: InspirationService,
) {
    @RabbitListener(queues = [RabbitMQConfig.QUEUE_NAME])
    fun receiveMessage(message: String) {
        log.info { "Received message: $message" }
    }

    /**
     * 同步同款标签
     */
    @RabbitListener(
        bindings = [QueueBinding(
            value = Queue(value = MqConstant.INSPIRATION_SYNC_SIMILAR_LABEL_QUEUE, declare = "true", autoDelete = "false"),
            exchange = Exchange(
                value = MqConstant.INSPIRATION_SYNC_SIMILAR_LABEL_EXCHANGE,
                declare = "true",
                autoDelete = "false",
                type = ExchangeTypes.DIRECT
            ),
            key = [MqConstant.INSPIRATION_SYNC_SIMILAR_LABEL_ROUTING_KEY]
        )]
    )
    fun syncSimilarLabel(message: Message, channel: Channel) {
        handle("同步同款标签", message, channel) {
            log.info { "同步同款标签 req = ${it.toJson(Inspiration::class.java)}" }
            inspirationService.syncSimilarLabel(
                it.toJson(Inspiration::class.java)
                    .also { log.info { "同步同款标签,success [${it.toJson()}]" } })
        }
    }


//    @RabbitListener(
//        bindings = [QueueBinding(
//            value = Queue(value = MqConstant.INSPIRATION_AIDC_UPDATE_QUEUE, declare = "true", autoDelete = "false"),
//            exchange = Exchange(
//                value = MqConstant.INSPIRATION_AIDC_UPDATE_EXCHANGE,
//                declare = "true",
//                autoDelete = "false",
//                type = ExchangeTypes.DIRECT
//            ),
//            key = [MqConstant.INSPIRATION_AIDC_UPDATE_ROUTING_KEY]
//        )]
//    )
//    fun gen(message: Message, channel: Channel) {
//        handle("更新灵感,消费失败", message, channel) {
//            log.info { "consumer history to update = ${it.toJson(InspirationAidcSourceHistory::class.java)}" }
//            inspirationAidcService.reflashInspiration(
//                it.toJson(InspirationAidcSourceHistory::class.java)
//                    .also { log.info { "更新灵感,ae外部灵感[${it.toJson()}]" } })
//        }
//    }


    private fun handle(error: String, message: Message, channel: Channel, fn: (Message) -> Unit) {
        try {
            val headers = message.messageProperties.headers
            UserUtils.mockUserInvoke(
                headers["operatorId"]?.toString()?.toLong() ?: AigcConstant.ACCESS_USER_ID,
                headers["operatorName"]?.toString() ?: AigcConstant.ACCESS_USER_NAME,
                headers["operateTenantId"]?.toString()?.toLong() ?: AigcConstant.TENANT_ID
            ) {
                fn(message)
            }
        } catch (e: Exception) {
            log.error(e) { "${error}\t${toStr(message, Charset.defaultCharset())}" }
        } finally {
            channel.basicAck(message.messageProperties.deliveryTag, false)
        }
    }

    private fun <T> Message.toJson(klass: Class<T>): T = toStr(this, Charset.defaultCharset()).parseJson(klass)

    private fun toStr(message: Message, charset: Charset): String = String(message.body, charset)

}