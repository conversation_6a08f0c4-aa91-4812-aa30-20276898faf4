package tech.tiangong.sdp.amqp

import jakarta.annotation.PostConstruct
import me.ahoo.cosid.IdGenerator
import org.springframework.amqp.core.Message
import org.springframework.amqp.core.MessageProperties
import org.springframework.amqp.rabbit.connection.CorrelationData
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.retry.support.RetryTemplate
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import java.io.IOException



@Slf4j
@Component
class RabbitProducer(
    private val rabbitTemplate: RabbitTemplate,
) {
    @PostConstruct
    fun init() {
        enhance()
    }


    fun send(message: EventMessage) =
        rabbitTemplate.send(message.exchange, message.routingKey, obtain(message), obtain())

    private fun obtain(message: EventMessage): Message {
        val properties = MessageProperties()
        val headers = properties.headers
        headers["operatorId"] = message.userId
        headers["operatorName"] = message.userName
        headers["operateTenantId"] = message.companyId
        headers["operateTime"] = message.dateTime
        return Message(message.message.toByteArray(), properties)
    }

    private fun obtain(): CorrelationData =
        CorrelationData(IdHelper.getId().toString())

    private fun enhance() {
        rabbitTemplate.setConfirmCallback { correlationData, ack, cause ->
            if (ack) {
                correlationData?.let {
                    log.info { "消息投递到exchange成功,correlationData:${it}" }
                } ?: run {
                    log.info { "消息投递到exchange成功" }
                }
            } else {
                correlationData?.let {
                    log.error { "消息投递exchange失败,原因:[$cause]，correlationData:${it}" }
                } ?: run {
                    log.error { "消息投递exchange失败,原因:[$cause]" }
                }
            }

        }
        rabbitTemplate.setReturnsCallback {
            log.error { "消息发送失败,应答码:${it.replyCode},交换机:${it.exchange},路由:${it.routingKey},原因:$it" }
        }
        rabbitTemplate.setRetryTemplate(
            RetryTemplate.builder().maxAttempts(3).exponentialBackoff(100, 2.0, 10000)
                .retryOn(
                    IOException::class.java
                ).traversingCauses().build()
        )
    }
}