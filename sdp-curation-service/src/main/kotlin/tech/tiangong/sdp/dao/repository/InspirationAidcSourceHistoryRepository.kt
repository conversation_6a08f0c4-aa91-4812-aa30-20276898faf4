package tech.tiangong.sdp.dao.repository

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import org.springframework.stereotype.Repository
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.sdp.dao.entity.InspirationAidcSource
import tech.tiangong.sdp.dao.entity.InspirationAidcSourceHistory
import tech.tiangong.sdp.dao.mapper.InspirationAidcSourceHistoryMapper


@Repository
class InspirationAidcSourceHistoryRepository :
    BaseRepository<InspirationAidcSourceHistoryMapper, InspirationAidcSourceHistory>() {

    fun listByInspirationId(inspirationId: Long): List<InspirationAidcSourceHistory> {
        return list(
            KtQueryWrapper(InspirationAidcSourceHistory::class.java)
                .eq(InspirationAidcSourceHistory::inspirationId, inspirationId)
                .eq(InspirationAidcSourceHistory::handleStatus, 0)
                .orderByDesc(InspirationAidcSourceHistory::inspirationCreatedTime)
        )
    }

}

