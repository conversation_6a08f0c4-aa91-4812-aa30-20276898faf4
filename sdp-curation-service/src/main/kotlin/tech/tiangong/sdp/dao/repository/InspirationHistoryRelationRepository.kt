package tech.tiangong.sdp.dao.repository

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import org.springframework.stereotype.Repository
import team.aikero.blade.data.mybatis.repository.BaseRepository
import tech.tiangong.sdp.dao.entity.InspirationHistoryRelation
import tech.tiangong.sdp.dao.mapper.InspirationHistoryRelationMapper


@Repository
class InspirationHistoryRelationRepository :
    BaseRepository<InspirationHistoryRelationMapper, InspirationHistoryRelation>() {
    fun listByInspirationId(inspirationId: Long): List<InspirationHistoryRelation> {
        return list(
            KtQueryWrapper(InspirationHistoryRelation::class.java)
                .eq(InspirationHistoryRelation::inspirationId, inspirationId)

        )
    }

}

