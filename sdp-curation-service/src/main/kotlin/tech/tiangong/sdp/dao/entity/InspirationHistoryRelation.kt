package tech.tiangong.sdp.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import team.aikero.blade.data.mybatis.entity.BaseEntityWithNamedAndReviser
import java.time.LocalDateTime

/**
 * 灵感-历史关系表
 */
@TableName(value = "inspiration_history_relation")
data class InspirationHistoryRelation(
    /**
     * 灵感关系id
     */
    @TableId(value = "relation_id", type = IdType.ASSIGN_ID)
    var relationId: Long? = null

) : BaseEntityWithNamedAndReviser() {
    /**
     * 灵感id(inspiration.inspiration_id)
     */
    @TableField(value = "inspiration_id")
    var inspirationId: Long? = null

    /**
     * 灵感历史id(inspiration_aidc_source_history.inspiration_history_id)
     */
    @TableField(value = "inspiration_history_id")
    var inspirationHistoryId: Long? = null
}


