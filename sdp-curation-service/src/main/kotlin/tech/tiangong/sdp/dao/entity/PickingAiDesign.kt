package tech.tiangong.sdp.dao.entity

import com.baomidou.mybatisplus.annotation.*

import team.aikero.blade.data.mybatis.entity.BaseEntityWithNamedAndReviser

import java.time.LocalDateTime

/**
 * 选款-AI设计(PickingAiDesign)表名: picking_ai_design
 *
 * <AUTHOR>
 * @since 2025-01-14 11:08:49
 */
@TableName(value = "picking_ai_design")
data class PickingAiDesign(
    /**
     * 选款id
     */
    @TableId(value = "picking_id", type = IdType.ASSIGN_ID)
    var pickingId: Long? = null,

    /**
     * 灵感数据id
     */
    @TableField(value = "inspiration_id")
    var inspirationId: Long? = null,
    /**
     * 灵感图
     */
    @TableField(value = "inspiration_image")
    var inspirationImage: String? = null,
    /**
     * 原图
     */
    @TableField(value = "source_image")
    var sourceImage: String? = null,
    /**
     * 灵感来源，ins、shein等
     */
    @TableField(value = "inspiration_source_type")
    var inspirationSourceType: String? = null,
    /**
     * 企划来源code
     */
    @TableField(value = "planning_source_code")
    var planningSourceCode: String? = null,
    /**
     * 企划来源
     */
    @TableField(value = "planning_source_name")
    var planningSourceName: String? = null,
    /**
     * 供给方式code
     */
    @TableField(value = "supply_method_code")
    var supplyMethodCode: String? = null,
    /**
     * 供给方式name
     */
    @TableField(value = "supply_method_name")
    var supplyMethodName: String? = null,
    /**
     * ai任务id
     */
    @TableField(value = "design_task_id")
    var designTaskId: Long? = null,
    /**
     * ai任务编号
     */
    @TableField(value = "design_task_code")
    var designTaskCode: String? = null,
    /**
     * 商品链接URL
     */
    @TableField(value = "product_link")
    var productLink: String? = null,
    /**
     * 数据来源(导入..)
     */
    @TableField(value = "data_source")
    var dataSource: String? = null,
    /**
     * 国家站点，获取当前lazada跨境的6个站点
     */
    @TableField(value = "country_site_code")
    var countrySiteCode: String? = null,
    /**
     * 国家站点，获取当前lazada跨境的6个站点
     */
    @TableField(value = "country_site_name")
    var countrySiteName: String? = null,
    /**
     * 外部品类
     */
    @TableField(value = "external_category")
    var externalCategory: String? = null,
    /**
     * 识别品类
     */
    @TableField(value = "identify_category_code")
    var identifyCategoryCode: String? = null,
    /**
     * 识别品类
     */
    @TableField(value = "identify_category_name")
    var identifyCategoryName: String? = null,
    /**
     * 波次编号
     */
    @TableField(value = "wave_batch_code")
    var waveBatchCode: String? = null,
    /**
     * 划线价(US)
     */
    @TableField(value = "retail_price")
    var retailPrice: String? = null,
    /**
     * 销售价(US)
     */
    @TableField(value = "sale_price")
    var salePrice: String? = null,
    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    var tenantId: Long? = null,
) : BaseEntityWithNamedAndReviser()


