package tech.tiangong.sdp.dao.entity

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import team.aikero.blade.data.mybatis.entity.BaseEntityWithNamedAndReviser
import java.time.LocalDateTime

/**
 * inspiration_aidc_source 表的子表，表示版本的概念
 */
@TableName(value = "inspiration_aidc_source_history")
data class InspirationAidcSourceHistory(
    /**
     * 灵感id
     */
    @TableId(value = "inspiration_history_id", type = IdType.ASSIGN_ID)
    var inspirationHistoryId: Long? = null

) : BaseEntityWithNamedAndReviser() {
    /**
     * 灵感id(inspiration_aidc_source.inspiration_id)
     */
    @TableField(value = "inspiration_id")
    var inspirationId: Long? = null

    /**
     * 第三方灵感id
     */
    @TableField(value = "third_inspiration_id")
    var thirdInspirationId: String? = null

    /**
     * 第三方任务id
     */
    @TableField(value = "third_task_id")
    var thirdTaskId: String? = null

    /**
     * 第三方品池策略id
     */
    @TableField(value = "third_pool_id")
    var thirdPoolId: String? = null

    /**
     * 第三方灵感信息
     */
    @TableField(value = "third_inspiration_info")
    var thirdInspirationInfo: String? = null

    /**
     * 企划来源code
     */
    @TableField(value = "planning_source_code")
    var planningSourceCode: String? = null

    /**
     * 企划来源name
     */
    @TableField(value = "planning_source_name")
    var planningSourceName: String? = null

    /**
     * 灵感图来源
     */
    @TableField(value = "inspiration_image_source")
    var inspirationImageSource: String? = null

    /**
     * 导入灵感图原图URL
     */
    @TableField(value = "source_image")
    var sourceImage: String? = null

    /**
     * 商品链接URL
     */
    @TableField(value = "product_link")
    var productLink: String? = null

    /**
     * 外部品类
     */
    @TableField(value = "external_category")
    var externalCategory: String? = null

    /**
     * 来源国家站点code
     */
    @TableField(value = "country_site_code")
    var countrySiteCode: String? = null

    /**
     * 来源国家站点name
     */
    @TableField(value = "country_site_name")
    var countrySiteName: String? = null

    /**
     * 划线价(US)
     */
    @TableField(value = "retail_price")
    var retailPrice: String? = null

    /**
     * 销售价(US)
     */
    @TableField(value = "sale_price")
    var salePrice: String? = null

    /**
     * 灵感创建时间
     */
    @TableField(value = "inspiration_created_time")
    var inspirationCreatedTime: LocalDateTime? = null

    /**
     * 数据来源
     */
    @TableField(value = "data_source")
    var dataSource: String? = null

    /**
     * 处理状态: 0待处理,1已处理
     */
    @TableField(value = "handle_status")
    var handleStatus: Int? = null

    /**
     * 处理信息
     */
    @TableField(value = "handle_message")
    var handleMessage: String? = null
}


