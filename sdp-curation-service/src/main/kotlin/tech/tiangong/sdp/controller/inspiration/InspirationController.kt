package tech.tiangong.sdp.controller.inspiration

import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.lang3.StringUtils
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.failed
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.convert.InspirationConvert
import tech.tiangong.sdp.req.InspirationImitationReConfirmReq
import tech.tiangong.sdp.req.inspiration.*
import tech.tiangong.sdp.resp.inspiration.InspirationDetailResp
import tech.tiangong.sdp.resp.inspiration.InspirationImportResultVo
import tech.tiangong.sdp.resp.inspiration.InspirationPageResp
import tech.tiangong.sdp.resp.inspiration.InspirationTaskSubmitResp
import tech.tiangong.sdp.service.InspirationService
import tech.tiangong.sdp.utils.joinToStr

/**
 * 灵感数据源 v3.13
 * <AUTHOR>
 * @date 2024/11/14 10:55
 */
@RestController
@RequestMapping("/web/v1/inspiration")
class InspirationController(
    private val inspirationService: InspirationService,
    private val commonExecutor: ThreadPoolTaskExecutor,
) {

    /**
     * 列表分页 v3.13
     * @param req 请求对象
     * @return
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: InspirationPageReq): DataResponse<PageVo<InspirationPageResp>> {
        return ok(inspirationService.page(req))
    }

    /**
     * 导出
     * @param req 请求对象
     * @return
     */
    @PostMapping("/export")
    fun export(response: HttpServletResponse, @Validated @RequestBody req: InspirationPageReq) {
        inspirationService.export(response, req)
    }

    /**
     * Excel导入 v3.10.1
     * @param file
     */
    @PostMapping("/import")
    fun importExcel(@RequestParam("file") file: MultipartFile): DataResponse<InspirationImportResultVo> {
        return ok(inspirationService.importExcel(file))
    }

    /**
     * 图片导入 v3.10.1
     * @param file
     */
    @PostMapping("/image/import")
    fun importImage(@Validated @RequestBody req: InspirationImportImageReq): DataResponse<Unit> {
        inspirationService.importImage(req)
        return ok()
    }

    /**
     * 详情 v3.13
     * @param inspirationId
     */
    @PostMapping("/detail/{inspirationId}")
    fun detail(@PathVariable inspirationId: Long): DataResponse<InspirationDetailResp> {
        return ok(inspirationService.detail(inspirationId))
    }

    /**
     * 提交任务 v3.13
     * @param req
     */
    @PostMapping("/task/submit")
    fun taskSubmit(@Validated @RequestBody req: InspirationTaskSubmitReq): DataResponse<Unit> {
        log.info { "taskSubmit req: ${req.toJson()}" }
        // 同步执行
        val requests = InspirationConvert.convert(req)
        log.info { "taskSubmit converted requests: ${requests.toJson()}" }
        if (requests.size > 1) {
            // 多个提交，品类和同步品类不要传
            requests.forEach {
                if (it.syncCategory == 1) {
                    throw RuntimeException("同步品类不支持多个提交")
                }
                if (StringUtils.isNotBlank(it.categoryName) || StringUtils.isNotBlank(it.categoryCode)) {
                    throw RuntimeException("品类不支持多个提交")
                }
            }
            // 异步执行
            requests.forEach {
                commonExecutor.execute { inspirationService.submitInspiration(it) }
            }
        } else {
            val inspirationSubmitReq = requests.first()
            inspirationSubmitReq.single = true
            inspirationService.submitInspiration(inspirationSubmitReq)
        }
        return ok()
    }

    /**
     * 重新提交-页面回显 v3.13
     * @param businessId
     */
    @PostMapping("/task/re-submit/detail/{businessId}")
    fun taskReSubmitDetail(@PathVariable businessId: Long): DataResponse<InspirationTaskSubmitResp> {
        return ok(inspirationService.taskReSubmitDetail(businessId))
    }


    /**
     * 删除 v3.10.1
     */
    @PostMapping("/remove")
    fun remove(@RequestBody inspirationIds: Set<Long>): DataResponse<Unit> {
        inspirationService.remove(inspirationIds)
        return ok()
    }

    /**
     * 加锁或者释放锁
     */
    @PostMapping("/lock-or-release")
    fun lockOrRelease(@RequestBody req: InspirationLockReq): DataResponse<Unit> {
        inspirationService.lockOrRelease(req)
        return ok()
    }

    /**
     * 淘汰
     */
    @PostMapping("/cancel")
    fun cancel(@RequestBody req: InspirationCancelReq): DataResponse<Unit> {
        inspirationService.cancel(req)
        return ok()
    }

    /**
     * 仿款确认 v3.13
     * @param req 仿款确认参数
     */
    @PostMapping("/imitation")
    fun imitationConfirm(
        @Validated @RequestBody req: InspirationImitationConfirmReq,
    ): DataResponse<String> {
        val resp = inspirationService.imitationConfirm(req)
        return if (resp.first.isNotEmpty()) {
            ok(resp.second.joinToStr(";")).also {
                log.info { "提交成功 resp=${it.toJson()}" }
            }
        } else {
            failed<String>(resp.second.joinToStr(";"))
                .also {
                    log.info { "提交失败 resp=${it.toJson()}" }
                }
        }
    }


    /**
     * 仿款确认(再次提交) v3.13
     * @param req 仿款确认参数
     */
    @PostMapping("/imitation/reconfirm")
    fun imitationReConfirm(
        @Validated @RequestBody req: InspirationImitationReConfirmReq,
    ): DataResponse<Unit> {
        inspirationService.imitationReConfirm(req)
        return ok()
    }


    /**
     * 仿款提交 v3.13
     * @param req 仿款提交参数
     */
    @PutMapping("/imitation")
    fun imitationSubmit(
        @Validated @RequestBody req: InspirationImitationSubmitReq,
    ): DataResponse<Unit> {
        inspirationService.imitationSubmit(req)
        return ok()
    }

}