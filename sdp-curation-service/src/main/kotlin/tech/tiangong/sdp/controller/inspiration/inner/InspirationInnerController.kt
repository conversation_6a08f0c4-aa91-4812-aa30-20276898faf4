package tech.tiangong.sdp.controller.inspiration.inner

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.auth.withUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.util.json.Json
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.common.req.AiDesignTaskCreateReq
import tech.tiangong.sdp.common.req.ProductOnlineNoticeReq
import tech.tiangong.sdp.common.resp.GetInspirationOrPickingIdResp
import tech.tiangong.sdp.common.resp.InspirationResp
import tech.tiangong.sdp.service.InspirationService
import tech.tiangong.sdp.service.LazadaService
import kotlin.math.log

/**
 * 灵感相关内部接口
 * <AUTHOR>
 * @date 2024-12-18 20:04:16
 */
@RestController
@RequestMapping("/inner/v1/inspiration")
@PreCheckIgnore
class InspirationInnerController(
    private val lazadaService: LazadaService,
    private val inspirationService: InspirationService,
) {
    /**
     * 商品上架通知
     * @param req
     * @return
     */
    @PostMapping("/notice/product/online")
    fun productOnlineNotice(@RequestBody req: ProductOnlineNoticeReq): DataResponse<Unit> {
        withSystemUser {
            lazadaService.pushAidcOnline(req.inspireSourceId, req.onlineSaleItemId)
        }
        return ok()
    }

    /**
     * 灵感id/选款id获取相关信息
     * @param inspirationPickingId
     * @return
     */
    @PostMapping("/get/inspirationOrPicking/{inspirationPickingId}")
    fun getByInspirationOrPickingId(@PathVariable("inspirationPickingId") inspirationPickingId: Long): DataResponse<GetInspirationOrPickingIdResp> {
        var resp = GetInspirationOrPickingIdResp()
        withSystemUser {
            resp = inspirationService.getByInspirationOrPickingId(inspirationPickingId)
        }
        return ok(resp)
    }

    /**
     * 根据灵感id获取信息
     */
    @PostMapping("/listByIds")
    fun listByIds(@Validated @RequestBody idList: List<Long>): DataResponse<List<InspirationResp>> {
        return ok(inspirationService.listByIds(idList))
    }


    /**
     * 提交AI设计任务
     * @param req
     */
    @PostMapping("/task/submit")
    fun submitAiDesignTask(@Validated @RequestBody req: AiDesignTaskCreateReq): DataResponse<Unit> {
        log.info { "提交AI设计任务 req=${req.toJson()}" }
        val currentUser = CurrentUser(
            id = req.creatorId,
            name = req.creatorName,
            code = "",
            tenantId = req.tenantId
        )
        withUser(currentUser) {
            inspirationService.submitAiDesignTask(req)
        }
        return ok()
    }

}
