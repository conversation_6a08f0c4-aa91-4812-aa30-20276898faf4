package tech.tiangong.sdp.controller.job

import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.sdp.service.InspirationService

/**
 * 灵感定时任务
 */
@RestController
@RequestMapping("/job/v1/inspiration")
@PreCheckIgnore
class JobInspirationController(
    private val inspirationService: InspirationService
) {

    /**
     * 定时插入相似款
     *
     */
    @PostMapping("/similar-style/create")
    fun createSimilarStyle(): DataResponse<Unit> {
        withSystemUser {
            inspirationService.createSimilarStyle()
        }
        return ok()
    }


}