package tech.tiangong.sdp.controller.picking.inner

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withUser
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.common.req.picking.AiDesignPickingReq
import tech.tiangong.sdp.req.picking.ImportOldPickingDataReq
import tech.tiangong.sdp.req.picking.UltraHdTaskCallbackReq
import tech.tiangong.sdp.service.AiDesignPickingService
import java.util.concurrent.Callable

/**
 * 选款相关内部接口
 * <AUTHOR>
 * @date 2025/1/8 16:14
 */
@RestController
@RequestMapping("/inner/v1/picking")
@PreCheckIgnore
class PickingStyleInnerController(
    private val aiDesignPickingService: AiDesignPickingService,
) {

    /**
     * AI设计创建选款
     *
     * @param req
     * @return
     */
    @PostMapping("/aigc/create")
    fun createByAiDesign(@Validated @RequestBody req: AiDesignPickingReq): DataResponse<Long> {
        if (req.creatorId == null || req.creatorName == null || req.tenantId == null) {
            throw BusinessException("创建人信息不能为空")
        }
        val user = CurrentUser(req.creatorId!!, req.creatorName!!, "", req.tenantId!!, false)
        val id: Long = withUser(user, Callable { aiDesignPickingService.createByAiDesign(req) });
        return ok(id)
    }

    /**
     * 导入旧选款数据(导入到结果表)
     *
     * @param req
     * @return
     */
    @PostMapping("/old/import")
    fun importOldPickingData(@Validated @RequestBody req: ImportOldPickingDataReq): DataResponse<Unit> {
        aiDesignPickingService.importOldPickingData(req)
        return ok()
    }

    /**
     * 4K图任务回调 v3.11
     */
    @PostMapping("/callback/ultra-hd-task")
    fun ultraHdTaskCallback(@Validated @RequestBody req: UltraHdTaskCallbackReq): DataResponse<Unit> {
        log.info { "ultraHdTaskCallback req=${req.toJson()}" }
        aiDesignPickingService.ultraHdTaskCallback(req)
        return ok()
    }
}