<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.sdp.dao.mapper.PickingAiDesignMapper">

    <resultMap type="tech.tiangong.sdp.dao.entity.PickingAiDesign" id="PickingAiDesignMap">
        <!--@mbg.generated-->
        <!--@Table picking_ai_design-->
        <id property="pickingId" column="picking_id" jdbcType="BIGINT"/>
        <result property="inspirationId" column="inspiration_id" jdbcType="BIGINT"/>
        <result property="inspirationImage" column="inspiration_image" jdbcType="VARCHAR"/>
        <result property="sourceImage" column="source_image" jdbcType="VARCHAR"/>
        <result property="inspirationSourceType" column="inspiration_source_type" jdbcType="VARCHAR"/>
        <result property="planningSourceCode" column="planning_source_code" jdbcType="VARCHAR"/>
        <result property="planningSourceName" column="planning_source_name" jdbcType="VARCHAR"/>
        <result property="supplyMethodCode" column="supply_method_code" jdbcType="VARCHAR"/>
        <result property="supplyMethodName" column="supply_method_name" jdbcType="VARCHAR"/>
        <result property="designTaskId" column="design_task_id" jdbcType="BIGINT"/>
        <result property="designTaskCode" column="design_task_code" jdbcType="VARCHAR"/>
        <result property="productLink" column="product_link" jdbcType="VARCHAR"/>
        <result property="dataSource" column="data_source" jdbcType="VARCHAR"/>
        <result property="countrySiteCode" column="country_site_code" jdbcType="VARCHAR"/>
        <result property="countrySiteName" column="country_site_name" jdbcType="VARCHAR"/>
        <result property="externalCategory" column="external_category" jdbcType="VARCHAR"/>
        <result property="identifyCategoryCode" column="identify_category_code" jdbcType="VARCHAR"/>
        <result property="identifyCategoryName" column="identify_category_name" jdbcType="VARCHAR"/>
        <result property="waveBatchCode" column="wave_batch_code" jdbcType="VARCHAR"/>
        <result property="retailPrice" column="retail_price" jdbcType="VARCHAR"/>
        <result property="salePrice" column="sale_price" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        picking_id
        ,inspiration_id
        ,inspiration_image
        ,source_image
        ,inspiration_source_type
        ,planning_source_code
        ,planning_source_name
        ,supply_method_code
        ,supply_method_name
        ,design_task_id
        ,design_task_code
        ,product_link
        ,data_source
        ,country_site_code
        ,country_site_name
        ,external_category
        ,identify_category_code
        ,identify_category_name
        ,wave_batch_code
        ,retail_price
        ,sale_price
        ,tenant_id
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>

    <select id="selectListPage" resultType="tech.tiangong.sdp.dao.entity.PickingAiDesign">
        select *
        from (select design.*
                from picking_ai_design_style style
                    join picking_ai_design design on style.picking_id = design.picking_id
                <where>
                    style.deleted = 0
                    and design.deleted = 0
                    <if test="req.externalCategory != null and req.externalCategory != ''">
                        and design.external_category like concat('%',#{req.externalCategory},'%')
                    </if>
                    <if test="req.pickingCreatorName != null and req.pickingCreatorName != ''">
                        and style.creator_name like concat('%',#{req.pickingCreatorName},'%')
                    </if>
                    <if test="req.pickingStartTime != null and req.pickingEndTime != null">
                        and style.created_time between #{req.pickingStartTime} and #{req.pickingEndTime}
                    </if>
                    <if test="req.inspirationSource != null and req.inspirationSource != ''">
                        and design.inspiration_source_type = #{req.inspirationSource}
                    </if>
                    <if test="req.countrySiteCode != null and req.countrySiteCode != ''">
                        and design.country_site_code = #{req.countrySiteCode}
                    </if>
                    <if test="req.selectorId != null">
                        and style.selector_id = #{req.selectorId}
                    </if>
                    <if test="req.waveBatchCode != null and req.waveBatchCode != ''">
                    and design.wave_batch_code = #{req.waveBatchCode}
                    </if>
                    <if test="req.pickingState != null">
                        and style.picking_state = #{req.pickingState}
                    </if>
                    <if test="req.selectorName != null and req.selectorName != ''">
                        and style.selector_name like concat('%',#{req.selectorName},'%')
                    </if>
                    <if test="req.imagePickingStartTime != null and req.imagePickingEndTime != null">
                        and style.selection_time between #{req.imagePickingStartTime} and #{req.imagePickingEndTime}
                    </if>
                    <if test="req.creatorIds != null and req.creatorIds.size() > 0">
                        and design.creator_id in
                        <foreach item="creatorId" collection="req.creatorIds" open="(" separator="," close=")">
                            #{creatorId}
                        </foreach>
                    </if>
                    <if test="req.taskCode != null and req.taskCode.size() > 0">
                        and
                        <foreach item="code" collection="req.taskCode" open="(" separator="or" close=")">
                            design.design_task_code like concat('%',#{code},'%')
                        </foreach>
                    </if>
                </where>
                group by design.picking_id
                ) as tmp
        order by created_time desc
    </select>

    <select id="countPickingStatus" resultType="tech.tiangong.sdp.dao.bo.PickingStateCountBo">
        select style.picking_state as pickingState,
        count(1)            as count
        from picking_ai_design_style style
        join picking_ai_design design on style.picking_id = design.picking_id
        <where>
            style.deleted = 0
            and design.deleted = 0
            <if test="req.externalCategory != null and req.externalCategory != ''">
                and design.external_category like concat('%',#{req.externalCategory},'%')
            </if>
            <if test="req.pickingCreatorName != null and req.pickingCreatorName != ''">
                and style.creator_name like concat('%',#{req.pickingCreatorName},'%')
            </if>
            <if test="req.pickingStartTime != null and req.pickingEndTime != null">
                and style.created_time between #{req.pickingStartTime} and #{req.pickingEndTime}
            </if>
            <if test="req.inspirationSource != null and req.inspirationSource != ''">
                and design.inspiration_source_type = #{req.inspirationSource}
            </if>
            <if test="req.countrySiteCode != null and req.countrySiteCode != ''">
                and design.country_site_code = #{req.countrySiteCode}
            </if>
            <if test="req.selectorId != null">
                and style.selector_id = #{req.selectorId}
            </if>
            <if test="req.waveBatchCode != null and req.waveBatchCode != ''">
                and design.wave_batch_code = #{req.waveBatchCode}
            </if>
            <if test="req.selectorName != null and req.selectorName != ''">
                and style.selector_name like concat('%',#{req.selectorName},'%')
            </if>
            <if test="req.imagePickingStartTime != null and req.imagePickingEndTime != null">
                and style.selection_time between #{req.imagePickingStartTime} and #{req.imagePickingEndTime}
            </if>
        </where>
        group by style.picking_state
    </select>
</mapper>

