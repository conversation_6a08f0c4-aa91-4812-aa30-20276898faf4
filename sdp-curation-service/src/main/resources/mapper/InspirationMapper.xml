<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.sdp.dao.mapper.InspirationMapper">

    <resultMap type="tech.tiangong.sdp.dao.entity.Inspiration" id="InspirationMap">
        <!--@mbg.generated-->
        <!--@Table inspiration-->
        <id property="inspirationId" column="inspiration_id" jdbcType="BIGINT"/>
        <result property="inspirationCode" column="inspiration_code" jdbcType="VARCHAR"/>
        <result property="thirdInspirationId" column="third_inspiration_id" jdbcType="VARCHAR"/>
        <result property="thirdInspirationInfo" column="third_inspiration_info" jdbcType="VARCHAR"/>
        <result property="planningSourceCode" column="planning_source_code" jdbcType="VARCHAR"/>
        <result property="planningSourceName" column="planning_source_name" jdbcType="VARCHAR"/>
        <result property="waveBatchCode" column="wave_batch_code" jdbcType="VARCHAR"/>
        <result property="inspirationImage" column="inspiration_image" jdbcType="VARCHAR"/>
        <result property="inspirationImageSource" column="inspiration_image_source" jdbcType="VARCHAR"/>
        <result property="sourceImage" column="source_image" jdbcType="VARCHAR"/>
        <result property="productLink" column="product_link" jdbcType="VARCHAR"/>
        <result property="externalCategory" column="external_category" jdbcType="VARCHAR"/>
        <result property="countrySiteCode" column="country_site_code" jdbcType="VARCHAR"/>
        <result property="countrySiteName" column="country_site_name" jdbcType="VARCHAR"/>
        <result property="retailPrice" column="retail_price" jdbcType="VARCHAR"/>
        <result property="salePrice" column="sale_price" jdbcType="VARCHAR"/>
        <result property="suggestedSupplyModeCode" column="suggested_supply_mode_code" jdbcType="VARCHAR"/>
        <result property="suggestedSupplyModeName" column="suggested_supply_mode_name" jdbcType="VARCHAR"/>
        <result property="inspirationCreatedTime" column="inspiration_created_time" jdbcType="TIMESTAMP"/>
        <result property="dataSource" column="data_source" jdbcType="VARCHAR"/>
        <result property="submitCount" column="submit_count" jdbcType="INTEGER"/>
        <result property="submitStatus" column="submit_status" jdbcType="INTEGER"/>
        <result property="lastSubmitTime" column="last_submit_time" jdbcType="TIMESTAMP"/>
        <result property="submitPushAidc" column="submit_push_aidc" jdbcType="INTEGER"/>
        <result property="onlinePushAidc" column="online_push_aidc" jdbcType="INTEGER"/>
        <result property="identifiedId" column="identified_id" jdbcType="BIGINT"/>
        <result property="identifiedStatus" column="identified_status" jdbcType="INTEGER"/>
        <result property="identifiedMessage" column="identified_message" jdbcType="VARCHAR"/>
        <result property="identifiedCategory" column="identified_category" jdbcType="VARCHAR"/>
        <result property="identifiedCategoryCode" column="identified_category_code" jdbcType="VARCHAR"/>
        <result property="identifiedLabel" column="identified_label" jdbcType="VARCHAR"/>
        <result property="fabricLabelMarketName" column="fabric_label_market_name" jdbcType="VARCHAR"/>
        <result property="fabricLabelColorCode" column="fabric_label_color_code" jdbcType="VARCHAR"/>
        <result property="fabricLabelColorHue" column="fabric_label_color_hue" jdbcType="VARCHAR"/>
        <result property="styleType" column="style_type" jdbcType="INTEGER"/>
        <result property="message" column="message" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        inspiration_id
        ,inspiration_code
        ,third_inspiration_id
        ,third_inspiration_info
        ,planning_source_code
        ,planning_source_name
        ,wave_batch_code
        ,inspiration_image
        ,inspiration_image_source
        ,source_image
        ,product_link
        ,external_category
        ,country_site_code
        ,country_site_name
        ,retail_price
        ,sale_price
        ,suggested_supply_mode_code
        ,suggested_supply_mode_name
        ,inspiration_created_time
        ,data_source
        ,submit_count
        ,submit_status
        ,last_submit_time
        ,submit_push_aidc
        ,online_push_aidc
        ,identified_id
        ,identified_status
        ,identified_message
        ,identified_category
        ,identified_category_code
        ,identified_label
        ,fabric_label_market_name
        ,fabric_label_color_code
        ,fabric_label_color_hue
        ,style_type
        ,message
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>


    <select id="selectListPage" resultType="tech.tiangong.sdp.dao.entity.Inspiration"
            parameterType="tech.tiangong.sdp.req.inspiration.InspirationPageReq">
        select *
        from inspiration
        <where>
            deleted = 0
            <if test="req.planningSourceCode != null and req.planningSourceCode != ''">
                and planning_source_code = #{req.planningSourceCode}
            </if>
            <if test="req.dataSourceCode != null and req.dataSourceCode != ''">
                and data_source = #{req.dataSourceCode}
            </if>
            <if test="req.externalCategory != null and req.externalCategory != ''">
                and external_category like concat('%',#{req.externalCategory},'%')
            </if>
            <if test="req.inspirationStartCreatedTime != null and req.inspirationEndCreatedTime != null">
                and created_time between #{req.inspirationStartCreatedTime} and #{req.inspirationEndCreatedTime}
            </if>
            <if test="req.suggestedSupplyModeCode != null and req.suggestedSupplyModeCode != ''">
                and suggested_supply_mode_code = #{req.suggestedSupplyModeCode}
            </if>
            <if test="req.inspirationSource != null and req.inspirationSource != ''">
                and inspiration_image_source like concat('%',#{req.inspirationSource},'%')
            </if>
            <if test="req.sourceCountrySiteCode != null and req.sourceCountrySiteCode != ''">
                and country_site_code = #{req.sourceCountrySiteCode}
            </if>
            <if test="req.identifiedResult != null and req.identifiedResult != ''">
                and identified_status = #{req.identifiedResult}
            </if>
            <if test="req.creatorName != null and req.creatorName != ''">
                and creator_name like concat('%',#{req.creatorName},'%')
            </if>
            <if test="req.inspirationSubmitCount != null">
                and submit_count = #{req.inspirationSubmitCount}
            </if>
            <if test="req.submitStatus != null">
                and submit_status = #{req.submitStatus}
            </if>
            <if test="req.submitterName != null and req.submitterName != ''">
                and (inspiration_id in (select inspiration_id from submit_downstream_log where creator_name like concat('%',#{req.submitterName},'%') and deleted = 0)
                or lock_name like concat('%',#{req.submitterName},'%'))

            </if>
            <if test="req.creatorIds != null and req.creatorIds.size() > 0">
                and creator_id in
                <foreach item="creatorId" collection="req.creatorIds" open="(" separator="," close=")">
                    #{creatorId}
                </foreach>
            </if>
            <if test="req.inspirationCode != null and req.inspirationCode != ''">
                and inspiration_code like concat('%',#{req.inspirationCode},'%')
            </if>
            <if test="req.waveBatchCode != null and req.waveBatchCode != ''">
                and wave_batch_code like concat('%',#{req.waveBatchCode},'%')
            </if>
            <if test="req.inspirationStartUpdateTime != null or req.inspirationEndUpdateTime != null">
                and EXISTS (
                SELECT 1
                FROM
                inspiration_history_relation AS ihr
                JOIN
                inspiration_aidc_source_history AS iash
                ON ihr.inspiration_history_id = iash.inspiration_history_id
                WHERE
                inspiration.inspiration_id = ihr.inspiration_id
                <if test="req.inspirationStartUpdateTime != null">
                    and iash.inspiration_created_time &gt;= #{req.inspirationStartUpdateTime}
                </if>
                <if test="req.inspirationEndUpdateTime != null">
                    AND iash.inspiration_created_time &lt;= #{req.inspirationEndUpdateTime}
                </if>
                )
            </if>
            <if test="req.similarStyleLabels != null and req.similarStyleLabels.size() > 0">
                and
                <foreach item="similarStyleLabel" collection="req.similarStyleLabels" open="(" separator="or" close=")">
                    similar_style_label like concat('%', #{similarStyleLabel},'%')
                </foreach>
            </if>
        </where>

        <choose>
            <when test="req.submitStatus != null and req.submitStatus == 1">
                order by last_submit_time desc
            </when>
            <otherwise>
                order by created_time desc
            </otherwise>
        </choose>
    </select>

    <update id="updateCreateById" parameterType="tech.tiangong.sdp.dao.entity.Inspiration">
        update inspiration
        <set>
            creator_id = #{creatorId}
            creator_name = #{creatorName}
        </set>
        where inspiration_id = #{inspirationId}
    </update>
</mapper>
