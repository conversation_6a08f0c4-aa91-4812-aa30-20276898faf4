<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.sdp.dao.mapper.InspirationAidcSourceMapper">

    <resultMap type="tech.tiangong.sdp.dao.entity.InspirationAidcSource" id="InspirationAidcSourceMap">
        <!--@mbg.generated-->
        <!--@Table inspiration_aidc_source-->
        <id property="inspirationId" column="inspiration_id" jdbcType="BIGINT"/>
        <result property="thirdInspirationId" column="third_inspiration_id" jdbcType="VARCHAR"/>
        <result property="thirdInspirationInfo" column="third_inspiration_info" jdbcType="VARCHAR"/>
        <result property="planningSourceCode" column="planning_source_code" jdbcType="VARCHAR"/>
        <result property="planningSourceName" column="planning_source_name" jdbcType="VARCHAR"/>
        <result property="inspirationImageSource" column="inspiration_image_source" jdbcType="VARCHAR"/>
        <result property="sourceImage" column="source_image" jdbcType="VARCHAR"/>
        <result property="productLink" column="product_link" jdbcType="VARCHAR"/>
        <result property="externalCategory" column="external_category" jdbcType="VARCHAR"/>
        <result property="countrySiteCode" column="country_site_code" jdbcType="VARCHAR"/>
        <result property="countrySiteName" column="country_site_name" jdbcType="VARCHAR"/>
        <result property="retailPrice" column="retail_price" jdbcType="VARCHAR"/>
        <result property="salePrice" column="sale_price" jdbcType="VARCHAR"/>
        <result property="inspirationCreatedTime" column="inspiration_created_time" jdbcType="TIMESTAMP"/>
        <result property="dataSource" column="data_source" jdbcType="VARCHAR"/>
        <result property="handleStatus" column="handle_status" jdbcType="INTEGER"/>
        <result property="handleMessage" column="handle_message" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        inspiration_id
        ,third_inspiration_id
        ,third_inspiration_info
        ,planning_source_code
        ,planning_source_name
        ,inspiration_image_source
        ,source_image
        ,product_link
        ,external_category
        ,country_site_code
        ,country_site_name
        ,retail_price
        ,sale_price
        ,inspiration_created_time
        ,data_source
        ,handle_status
        ,handle_message
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>

    <delete id="deletePhysicalById" parameterType="java.lang.Long">
        delete from inspiration_aidc_source
        where inspiration_id = #{inspirationId,jdbcType=BIGINT}
    </delete>
</mapper>

