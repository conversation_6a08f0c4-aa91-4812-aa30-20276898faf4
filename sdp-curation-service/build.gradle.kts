import java.time.LocalDate
import java.time.format.DateTimeFormatter

plugins {
    alias(commonLibs.plugins.springboot)
    alias(commonLibs.plugins.google.ksp)
    alias(commonLibs.plugins.kapt)
    alias(commonLibs.plugins.kotlin.jvm)
    alias(commonLibs.plugins.kotlin.spring)
    alias(commonLibs.plugins.publish.conf)
    alias(commonLibs.plugins.common.conf)
}

dependencies {
    implementation(projects.sdpCurationCommon)

    implementation(commonLibs.blade.web.cloud.spring.boot.starter)
    implementation(commonLibs.mysql.connector)
//    implementation(commonLibs.blade.oplog.spring.boot.starter)
    implementation(commonLibs.blade.data.mybatis.plus.spring.boot.starter)
    implementation(commonLibs.blade.sequence.spring.boot.starter)

    implementation(commonLibs.fastjson2kotlin)
    implementation(commonLibs.jackson.module.kotlin)
    //jimmer
//    implementation(commonLibs.blade.jimmer.spring.boot.starter)
//    ksp(commonLibs.jimmer.ksp)
    //mapstruct
    implementation(commonLibs.mapstruct)
    kapt(commonLibs.mapstruct.processor)
//    kapt(commonLibs.jimmer.mapstruct.apt)
    implementation(springBootLibs.spring.springBootStarterAmqp)
    testImplementation(commonLibs.blade.test.spring.boot.starter)
    //其他工具包
//    implementation(libs.google.guava)
    implementation(libs.easyexcel){
        exclude("org.apache.poi","poi-ooxml-schemas")
    }
    implementation(libs.easypoi.base){
        exclude("org.apache.poi","poi-ooxml-schemas")
    }
//    implementation(libs.pagehelper)
//    implementation(libs.commons.lang3)

    //sdk
//    implementation("tech.tiangong.inspiration:inspiration-sdk:3.2.0-SNAPSHOT")
//    implementation("tech.tiangong.pop:pop-product-sdk:0.0.12-SNAPSHOT")
//    implementation("tech.tiangong.sdp:sdp-design-service-sdk:1.0.4-RELEASE")
//    implementation("tech.tiangong.sdp:sdp-design-common:1.0.2-RELEASE")
    implementation("tech.tiangong.fashion:aigc-digital-print-sdk:3.0.4") {
        exclude("org.slf4j")
    }
    implementation("team.aikero.arsenal:dict-sdk:0.0.2")
    implementation("tech.tiangong.butted:butted-sdk:3.1.2-RELEASE")
    implementation("tech.tiangong.pop:pop-product-sdk:0.0.1")
    implementation("com.lazada:lazop-api-sdk:1.2.0")

    implementation(commonLibs.blade.auth.spring.boot.starter)
    implementation(commonLibs.blade.feign.spring.boot.starter)
//    implementation("com.arsenal:file-manager-starter:0.0.4-SNAPSHOT")
    implementation(commonLibs.blade.lock.spring.boot.starter)
    implementation(commonLibs.blade.data.redis.spring.boot.starter)

    implementation(commonLibs.blade.file)
    implementation(commonLibs.blade.file.spring.boot.starter)
    implementation(libs.bfg.sdk)

}

tasks.bootJar {
    archiveVersion.set("")
}

// 标定使用JUnit平台
tasks.test {
    useJUnitPlatform()
}

kapt {
    keepJavacAnnotationProcessors = true
}


tasks.register("updateVersionProperties") {
    doLast {
        val versionPropertiesFile = file("$projectDir/src/main/resources/versions.properties")
        val currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        versionPropertiesFile.writeText("spring.cloud.nacos.discovery.metadata.version=$currentDate")
    }
}

tasks.named("compileKotlin") {
    dependsOn("updateVersionProperties")
}


configurations.all {
    resolutionStrategy {
//        force("javax.validation:validation-api:2.0.1.Final")
//        force("org.slf4j:slf4j-api:1.7.36")
    }
}